const mallData = {
  1: {
    id: 1,
    name: "Grand Central Plaza",
    location: "Sector 15, Noida, Uttar Pradesh 201301, India",
    image:
      "https://images.pexels.com/photos/54581/escalator-stairs-metal-segments-architecture-54581.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Grand Central Plaza is a premier shopping destination in Noida, offering a vibrant mix of fashion, dining, and entertainment across 4 floors with over 160 stores. Known for its seasonal fashion festivals and live music events.",
    rating: 4.8,
    floors: 4,
    shopCount: 160,
    featured: true,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 ************",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "Kids Zone",
      "WiFi",
      "ATM",
      "Customer Service",
      "Gaming Zone",
      "Pet-Friendly Area"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Electronics", "Beauty", "Sports", "Health & Wellness"],
    shopsByFloor: {
      1: {
        Electronics: [
          {
            id: 1,
            name: "Reliance Digital",
            description: "Latest electronics with exclusive product launches and tech workshops",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            featured: true,
            specialFeatures: ["Tech Workshops", "Extended Warranty", "Online Pre-Booking"]
          },
          {
            id: 43,
            name: "Apple Store",
            description: "Official Apple products with hands-on demos and repair services",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Genius Bar", "Trade-In Program"]
          }
        ],
        Fashion: [
          {
            id: 2,
            name: "Lifestyle",
            description: "Trendy fashion for men, women, and kids with seasonal collections",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Loyalty Discounts", "Personal Styling"]
          },
          {
            id: 44,
            name: "Zara",
            description: "Global fashion brand with sustainable clothing lines",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Eco-Friendly Collections", "Online Returns"]
          }
        ],
        "Food & Beverage": [
          {
            id: 3,
            name: "Haldiram's",
            description: "Authentic Indian snacks and sweets with live cooking demos",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Live Cooking", "Custom Gift Hampers"]
          }
        ],
        Sports: [
          {
            id: 4,
            name: "Decathlon",
            description: "Sports equipment and activewear with in-store fitness trials",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Fitness Trials", "Equipment Rentals"]
          }
        ]
      },
      2: {
        Fashion: [
          {
            id: 5,
            name: "Pantaloons",
            description: "Fashion for the entire family with exclusive festive collections",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Festive Sales", "Kids’ Fashion Shows"]
          }
        ],
        Beauty: [
          {
            id: 6,
            name: "Nykaa Luxe",
            description: "Premium beauty products with free makeup consultations",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Makeup Consultations", "Online Booking"]
          },
          {
            id: 8,
            name: "Lakmé Salon",
            description: "Beauty services with bridal and event styling packages",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Bridal Packages", "Loyalty Program"]
          }
        ],
        Electronics: [
          {
            id: 7,
            name: "Croma",
            description: "Consumer electronics with same-day delivery options",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Same-Day Delivery", "Tech Support"]
          }
        ]
      },
      3: {
        "Food & Beverage": [
          {
            id: 9,
            name: "Food Court",
            description: "Variety of Indian and global cuisines with live music evenings",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Live Music", "Themed Food Festivals"]
          },
          {
            id: 45,
            name: "Starbucks",
            description: "Global coffee chain with exclusive seasonal beverages",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Seasonal Drinks", "Loyalty Rewards"]
          }
        ],
        "Department Store": [
          {
            id: 10,
            name: "Big Bazaar",
            description: "Everything for home and lifestyle with weekly discount events",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Weekly Discounts", "Home Delivery"]
          }
        ],
        Electronics: [
          {
            id: 11,
            name: "Vijay Sales",
            description: "Electronics with financing options and tech support",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Financing Options", "Tech Support"]
          }
        ]
      },
      4: {
        Entertainment: [
          {
            id: 12,
            name: "PVR Cinemas",
            description: "Latest movies in premium theaters with IMAX screens",
            phone: "+91 ************",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["IMAX Screens", "Online Ticket Booking"]
          },
          {
            id: 13,
            name: "Kids Play Zone",
            description: "Fun activities for children with supervised workshops",
            phone: "+91 ************",
            hours: "10:00 AM - 8:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Supervised Workshops", "Birthday Party Hosting"]
          }
        ],
        Services: [
          {
            id: 14,
            name: "VIP Lounge",
            description: "Premium shopping assistance and relaxation with concierge services",
            phone: "+91 ************",
            hours: "10:00 AM - 8:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Concierge Services", "Priority Shopping"]
          }
        ],
        "Health & Wellness": [
          {
            id: 46,
            name: "FitZone Gym",
            description: "Fitness center with personal training and yoga classes",
            phone: "+91 ************",
            hours: "8:00 AM - 10:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Personal Training", "Group Classes"]
          }
        ]
      }
    }
  },
  2: {
    id: 2,
    name: "City Centre Mall",
    location: "Salt Lake City, Kolkata, West Bengal 700064, India",
    image:
      "https://images.pexels.com/photos/264507/pexels-photo-264507.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "City Centre Mall in Kolkata offers a vibrant shopping experience with a mix of fashion, dining, and entertainment across 3 floors, known for its cultural events and art exhibitions.",
    rating: 4.5,
    floors: 3,
    shopCount: 130,
    featured: true,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 33 123 4567",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "WiFi",
      "ATM",
      "Customer Service",
      "Art Gallery"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Beauty", "Department Store"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 15,
            name: "Reliance Trends",
            description: "Affordable fashion with exclusive regional designs",
            phone: "+91 33 111 0015",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Regional Designs", "Loyalty Program"]
          },
          {
            id: 47,
            name: "FabIndia",
            description: "Traditional Indian clothing and handicrafts",
            phone: "+91 33 111 0047",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Handcrafted Collections", "Eco-Friendly Products"]
          }
        ],
        "Food & Beverage": [
          {
            id: 16,
            name: "Bikanervala",
            description: "Indian sweets and snacks with vegan options",
            phone: "+91 33 111 0016",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Vegan Menu", "Custom Orders"]
          }
        ]
      },
      2: {
        Fashion: [
          {
            id: 17,
            name: "Westside",
            description: "Contemporary Indian fashion with in-house designers",
            phone: "+91 33 111 0017",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["In-House Designs", "Seasonal Sales"]
          }
        ],
        Entertainment: [
          {
            id: 18,
            name: "INOX Cinemas",
            description: "Premium movie theaters with 4DX experience",
            phone: "+91 33 111 0018",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["4DX Screens", "Online Booking"]
          }
        ],
        Beauty: [
          {
            id: 48,
            name: "Sephora",
            description: "Global beauty brands with in-store makeovers",
            phone: "+91 33 111 0048",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["In-Store Makeovers", "Loyalty Rewards"]
          }
        ]
      },
      3: {
        "Department Store": [
          {
            id: 19,
            name: "Spencer's",
            description: "Retail for home and lifestyle with online shopping integration",
            phone: "+91 33 111 0019",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Online Shopping", "Home Delivery"]
          }
        ],
        Beauty: [
          {
            id: 20,
            name: "Colorbar",
            description: "Cosmetics with cruelty-free products",
            phone: "+91 33 111 0020",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Cruelty-Free Products", "Makeup Workshops"]
          }
        ]
      }
    }
  },
  3: {
    id: 3,
    name: "Phoenix Marketcity",
    location: "Whitefield Main Rd, Bengaluru, Karnataka 560048, India",
    image:
      "https://images.pexels.com/photos/7935363/pexels-photo-7935363.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Phoenix Marketcity in Bengaluru is a sprawling shopping and entertainment hub with over 210 stores across 4 floors, featuring international brands and a rooftop dining area.",
    rating: 4.7,
    floors: 4,
    shopCount: 210,
    featured: true,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 80 123 4567",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "Kids Zone",
      "WiFi",
      "ATM",
      "Customer Service",
      "Rooftop Dining"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Electronics", "Beauty", "Department Store"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 21,
            name: "Shoppers Stop",
            description: "Premium fashion and lifestyle with exclusive member benefits",
            phone: "+91 80 111 0021",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Member Benefits", "Personal Shopping"]
          },
          {
            id: 49,
            name: "H&M",
            description: "Global fashion with affordable trendy collections",
            phone: "+91 80 111 0049",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Sustainable Fashion", "Online Returns"]
          }
        ],
        "Food & Beverage": [
          {
            id: 22,
            name: "KFC",
            description: "Fast food with exclusive combo offers",
            phone: "+91 80 111 0022",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Combo Offers", "Drive-Thru"]
          }
        ]
      },
      2: {
        Electronics: [
          {
            id: 23,
            name: "Samsung Store",
            description: "Latest smartphones with in-store tech demos",
            phone: "+91 80 111 0023",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Tech Demos", "Trade-In Program"]
          }
        ],
        Beauty: [
          {
            id: 24,
            name: "MAC Cosmetics",
            description: "High-end makeup with professional artist sessions",
            phone: "+91 80 111 0024",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Professional Makeup Sessions", "Loyalty Program"]
          }
        ]
      },
      3: {
        Entertainment: [
          {
            id: 25,
            name: "PVR Cinemas",
            description: "Multiplex with premium screens and recliner seats",
            phone: "+91 80 111 0025",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Recliner Seats", "Online Booking"]
          }
        ],
        "Department Store": [
          {
            id: 26,
            name: "Lifestyle",
            description: "Fashion and home essentials with seasonal discounts",
            phone: "+91 80 111 0026",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Seasonal Discounts", "Online Shopping"]
          }
        ]
      },
      4: {
        "Food & Beverage": [
          {
            id: 27,
            name: "Food Court",
            description: "Diverse dining options with rooftop seating",
            phone: "+91 80 111 0027",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Rooftop Seating", "Live Food Counters"]
          }
        ],
        Services: [
          {
            id: 28,
            name: "Spa & Wellness",
            description: "Relaxation with massage and wellness therapies",
            phone: "+91 80 111 0028",
            hours: "10:00 AM - 8:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Wellness Therapies", "Online Booking"]
          }
        ]
      }
    }
  },
  4: {
    id: 4,
    name: "Orion Mall",
    location: "Dr Rajkumar Rd, Bengaluru, Karnataka 560055, India",
    image:
      "https://images.pexels.com/photos/2988232/pexels-photo-2988232.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Orion Mall in Bengaluru is a modern shopping destination with a focus on fashion, entertainment, and dining across 3 floors, featuring a lakeside view.",
    rating: 4.6,
    floors: 3,
    shopCount: 150,
    featured: false,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 80 123 4568",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "WiFi",
      "ATM",
      "Lakeside Seating"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Electronics", "Beauty"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 29,
            name: "Max Fashion",
            description: "Affordable clothing with family discount programs",
            phone: "+91 80 111 0029",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Family Discounts", "Online Shopping"]
          }
        ],
        "Food & Beverage": [
          {
            id: 30,
            name: "Domino's Pizza",
            description: "Pizzas with custom topping options",
            phone: "+91 80 111 0030",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Custom Toppings", "Express Delivery"]
          }
        ]
      },
      2: {
        Electronics: [
          {
            id: 31,
            name: "Reliance Digital",
            description: "Electronics with in-store tech support",
            phone: "+91 80 111 0031",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Tech Support", "Extended Warranty"]
          }
        ],
        Beauty: [
          {
            id: 32,
            name: "Nykaa",
            description: "Beauty products with virtual try-on feature",
            phone: "+91 80 111 0032",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Virtual Try-On", "Loyalty Rewards"]
          }
        ]
      },
      3: {
        Entertainment: [
          {
            id: 33,
            name: "INOX Cinemas",
            description: "Movie theaters with Dolby Atmos sound",
            phone: "+91 80 111 0033",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Dolby Atmos", "Online Booking"]
          }
        ],
        "Department Store": [
          {
            id: 34,
            name: "Spencer's",
            description: "Retail for daily needs with home delivery",
            phone: "+91 80 111 0034",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Home Delivery", "Loyalty Program"]
          }
        ]
      }
    }
  },
  5: {
    id: 5,
    name: "Lulu Mall",
    location: "Edappally, Kochi, Kerala 682024, India",
    image:
      "https://images.pexels.com/photos/337909/pexels-photo-337909.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Lulu Mall in Kochi is one of the largest malls in India, featuring a wide range of shopping, dining, and entertainment options across 4 floors with a hypermarket.",
    rating: 4.9,
    floors: 4,
    shopCount: 190,
    featured: true,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 ************",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "Kids Zone",
      "WiFi",
      "ATM",
      "Customer Service",
      "Hypermarket"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Electronics", "Beauty", "Department Store"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 35,
            name: "Pantaloons",
            description: "Fashion for the entire family with exclusive festive collections",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Festive Collections", "Loyalty Program"]
          }
        ],
        "Food & Beverage": [
          {
            id: 36,
            name: "Barbeque Nation",
            description: "Indian barbecue dining with live grills",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Live Grills", "Buffet Discounts"]
          }
        ]
      },
      2: {
        Electronics: [
          {
            id: 37,
            name: "Croma",
            description: "Consumer electronics with financing options",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Financing Options", "Tech Support"]
          }
        ],
        Beauty: [
          {
            id: 38,
            name: "Lakmé Salon",
            description: "Beauty services with premium spa treatments",
            phone: "+91 ************",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Spa Treatments", "Online Booking"]
          }
        ]
      },
      3: {
        Entertainment: [
          {
            id: 39,
            name: "PVR Cinemas",
            description: "Premium movie theaters with 4K screens",
            phone: "+91 ************",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["4K Screens", "Online Booking"]
          }
        ],
        "Department Store": [
          {
            id: 40,
            name: "Big Bazaar",
            description: "Home and lifestyle products with weekly sales",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Weekly Sales", "Home Delivery"]
          }
        ]
      },
      4: {
        "Food & Beverage": [
          {
            id: 41,
            name: "Food Court",
            description: "Variety of dining options with international cuisines",
            phone: "+91 ************",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["International Cuisines", "Live Counters"]
          }
        ],
        Services: [
          {
            id: 42,
            name: "Kids Play Area",
            description: "Fun zone for children with interactive games",
            phone: "+91 ************",
            hours: "10:00 AM - 8:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Interactive Games", "Birthday Party Hosting"]
          }
        ]
      }
    }
  },
  6: {
    id: 6,
    name: "Ambience Mall",
    location: "Vasant Kunj, New Delhi, Delhi 110070, India",
    image:
      "https://images.pexels.com/photos/1778412/pexels-photo-1778412.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Ambience Mall in New Delhi is a luxury shopping destination with premium brands, fine dining, and entertainment across 4 floors, known for its upscale ambiance.",
    rating: 4.8,
    floors: 4,
    shopCount: 170,
    featured: false,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 11 123 4567",
    amenities: [
      "Free Parking",
      "Valet Parking",
      "Food Court",
      "Cinema",
      "Kids Zone",
      "WiFi",
      "ATM",
      "Customer Service",
      "Luxury Lounge"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Beauty", "Jewelry"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 50,
            name: "Marks & Spencer",
            description: "British fashion brand with premium clothing lines",
            phone: "+91 11 111 0050",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Premium Collections", "Loyalty Program"]
          }
        ],
        Jewelry: [
          {
            id: 51,
            name: "Tanishq",
            description: "Luxury jewelry with custom design services",
            phone: "+91 11 111 0051",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Custom Designs", "Gold Exchange"]
          }
        ]
      },
      2: {
        Beauty: [
          {
            id: 52,
            name: "Kiehl’s",
            description: "Skincare and beauty with personalized consultations",
            phone: "+91 11 111 0052",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Skin Consultations", "Eco-Friendly Products"]
          }
        ],
        Fashion: [
          {
            id: 53,
            name: "Forever 21",
            description: "Trendy fashion for young adults",
            phone: "+91 11 111 0053",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Youth Collections", "Seasonal Sales"]
          }
        ]
      },
      3: {
        "Food & Beverage": [
          {
            id: 54,
            name: "Theobroma",
            description: "Premium bakery with artisanal desserts",
            phone: "+91 11 111 0054",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Artisanal Desserts", "Custom Cakes"]
          }
        ],
        Entertainment: [
          {
            id: 55,
            name: "PVR Director’s Cut",
            description: "Luxury cinema with gourmet dining",
            phone: "+91 11 111 0055",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Gourmet Dining", "Recliner Seats"]
          }
        ]
      },
      4: {
        Services: [
          {
            id: 56,
            name: "Luxury Spa",
            description: "Premium spa with holistic treatments",
            phone: "+91 11 111 0056",
            hours: "10:00 AM - 8:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Holistic Treatments", "Online Booking"]
          }
        ],
        "Food & Beverage": [
          {
            id: 57,
            name: "Food Court",
            description: "Upscale dining with global cuisines",
            phone: "+91 11 111 0057",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Global Cuisines", "Rooftop Seating"]
          }
        ]
      }
    }
  },
  7: {
    id: 7,
    name: "Express Avenue",
    location: "Whites Rd, Chennai, Tamil Nadu 600014, India",
    image:
      "https://images.pexels.com/photos/258154/pexels-photo-258154.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Express Avenue in Chennai is a dynamic shopping hub with a mix of local and international brands across 4 floors, featuring a central atrium for events.",
    rating: 4.7,
    floors: 4,
    shopCount: 180,
    featured: true,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 44 123 4567",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "Kids Zone",
      "WiFi",
      "ATM",
      "Customer Service",
      "Event Atrium"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Electronics", "Home Decor"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 58,
            name: "Lifestyle",
            description: "Fashion and home essentials with exclusive sales",
            phone: "+91 44 111 0058",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Exclusive Sales", "Online Shopping"]
          }
        ],
        "Food & Beverage": [
          {
            id: 59,
            name: "Taco Bell",
            description: "Mexican-inspired fast food with vegan options",
            phone: "+91 44 111 0059",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Vegan Options", "Quick Service"]
          }
        ]
      },
      2: {
        Electronics: [
          {
            id: 60,
            name: "Reliance Digital",
            description: "Electronics with in-store demos and financing",
            phone: "+91 44 111 0060",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["In-Store Demos", "Financing Options"]
          }
        ],
        "Home Decor": [
          {
            id: 61,
            name: "Home Centre",
            description: "Furniture and home decor with interior design services",
            phone: "+91 44 111 0061",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Interior Design Services", "Home Delivery"]
          }
        ]
      },
      3: {
        Entertainment: [
          {
            id: 62,
            name: "SPI Cinemas",
            description: "Premium movie theaters with plush seating",
            phone: "+91 44 111 0062",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Plush Seating", "Online Booking"]
          }
        ],
        "Food & Beverage": [
          {
            id: 63,
            name: "Food Court",
            description: "Diverse cuisines with live cooking stations",
            phone: "+91 44 111 0063",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Live Cooking Stations", "Themed Events"]
          }
        ]
      },
      4: {
        Services: [
          {
            id: 64,
            name: "Kids Play Area",
            description: "Interactive play zone with educational games",
            phone: "+91 44 111 0064",
            hours: "10:00 AM - 8:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Educational Games", "Party Hosting"]
          }
        ],
        "Home Decor": [
          {
            id: 65,
            name: "IKEA",
            description: "Affordable furniture with in-store design consultations",
            phone: "+91 44 111 0065",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Design Consultations", "Home Delivery"]
          }
        ]
      }
    }
  },
  8: {
    id: 8,
    name: "Inorbit Mall",
    location: "Hitec City, Hyderabad, Telangana 500081, India",
    image:
      "https://images.pexels.com/photos/256219/pexels-photo-256219.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200",
    description:
      "Inorbit Mall in Hyderabad is a tech-savvy shopping destination with a mix of global and local brands across 3 floors, featuring a tech lounge and startup events.",
    rating: 4.6,
    floors: 3,
    shopCount: 140,
    featured: true,
    openingHours: ["10:00 AM - 10:00 PM"],
    openingHoursExceptions: ["Sunday"],
    phone: "+91 40 123 4567",
    amenities: [
      "Free Parking",
      "Food Court",
      "Cinema",
      "WiFi",
      "ATM",
      "Customer Service",
      "Tech Lounge"
    ],
    categories: ["Fashion", "Dining", "Entertainment", "Electronics", "Books"],
    shopsByFloor: {
      1: {
        Fashion: [
          {
            id: 66,
            name: "Westside",
            description: "Contemporary fashion with exclusive regional designs",
            phone: "+91 40 111 0066",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Regional Designs", "Loyalty Program"]
          }
        ],
        "Food & Beverage": [
          {
            id: 67,
            name: "Pizza Hut",
            description: "Pizzas with customizable options",
            phone: "+91 40 111 0067",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Customizable Pizzas", "Express Delivery"]
          }
        ]
      },
      2: {
        Electronics: [
          {
            id: 68,
            name: "Samsung Store",
            description: "Smartphones and electronics with trade-in offers",
            phone: "+91 40 111 0068",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Trade-In Offers", "Tech Demos"]
          }
        ],
        Books: [
          {
            id: 69,
            name: "Crossword",
            description: "Books and stationery with author events",
            phone: "+91 40 111 0069",
            hours: "10:00 AM - 9:00 PM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Author Events", "Online Ordering"]
          }
        ]
      },
      3: {
        Entertainment: [
          {
            id: 70,
            name: "INOX Cinemas",
            description: "Movie theaters with 3D screens",
            phone: "+91 40 111 0070",
            hours: "10:00 AM - 12:00 AM",
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["3D Screens", "Online Booking"]
          }
        ],
        "Food & Beverage": [
          {
            id: 71,
            name: "Food Court",
            description: "Variety of dining with tech-themed ambiance",
            phone: "+91 40 111 0071",
            hours: ["10:00 AM - 10:00 PM"],
            image: "/placeholder.svg?height=200&width=300",
            specialFeatures: ["Tech-Themed Ambiance", "Live Counters"]
          }
        ]
      }
    }
  }
};

const categories = [
  "All",
  "Beauty",
  "Books",
  "Department Store",
  "Dining",
  "Electronics",
  "Entertainment",
  "Fashion",
  "Food & Beverage",
  "Health & Wellness",
  "Home Decor",
  "Jewelry",
  "Services",
  "Sports"
];

const locations = [
  "All",
  "Sector 15, Noida, Uttar Pradesh 201301, India",
  "Salt Lake City, Kolkata, West Bengal 700064, India",
  "Whitefield Main Rd, Bengaluru, Karnataka 560048, India",
  "Dr Rajkumar Rd, Bengaluru, Karnataka 560055, India",
  "Edappally, Kochi, Kerala 682024, India",
  "Vasant Kunj, New Delhi, Delhi 110070, India",
  "Whites Rd, Chennai, Tamil Nadu 600014, India",
  "Hitec City, Hyderabad, Telangana 500081, India"
];

export { mallData, categories, locations };