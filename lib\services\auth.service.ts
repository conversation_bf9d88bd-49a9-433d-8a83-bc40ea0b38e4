import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { faker } from '@faker-js/faker';
import prisma from '../prisma';
import { User, UserRole } from '../generated/prisma';
import { ApiResponse } from '../types/api-response';

// Environment variables for JWT
const JWT_SECRET = process.env.JWT_SECRET || 'secret-key';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'refresh-secret-key';
export const ACCESS_TOKEN_EXPIRY = '15m'; // Access token expires in 15 minutes
export const REFRESH_TOKEN_EXPIRY = '7d'; // Refresh token expires in 7 days

// Interface for user registration input
export interface RegisterInput {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role?: UserRole;
}

// Interface for login input
export interface LoginInput {
  email: string;
  password: string;
}

// Interface for JWT payload
export interface JwtPayload {
  userId: string;
  email: string;
  role: UserRole;
}

// Register a new user
export async function registerUser(input: RegisterInput): Promise<{
  user: Pick<User, 'id' | 'name' | 'email' | 'role'>;
  accessToken: string;
  refreshToken: string;
}> {
  const { name, email, password, phone } = input;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({ where: { email } });
  if (existingUser) {
    throw new Error('User with this email already exists');
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 10);

  // Create user
  const user = await prisma.user.create({
    data: {
      name,
      email,
      phone,
      password: hashedPassword,
      role: UserRole.user,
      isActive: true,
      avatar: faker.image.avatar(),
      createdAt: new Date(),
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
    },
  });

  // Generate tokens
  const accessToken = generateAccessToken(user);
  const refreshToken = generateRefreshToken(user);

  return { user, accessToken, refreshToken };
}

// Login user
export async function loginUser(input: LoginInput): Promise<ApiResponse<{
  user: Pick<User, 'id' | 'name' | 'email' | 'role'>;
  accessToken: string;
  refreshToken: string;
}>> {
  const { email, password } = input;

  // Find user
  const user = await prisma.user.findUnique({
    where: { email },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      password: true,
      isActive: true,
    },
  });

  if (!user || !user.isActive) {
    throw new Error('Invalid credentials or inactive user');
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    throw new Error('Invalid credentials');
  }

  // Generate tokens
  const accessToken = generateAccessToken(user);
  const refreshToken = generateRefreshToken(user);

  // Update last login
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() },
  });

  return {
    success: true,
    data: {
      user: { id: user.id, name: user.name, email: user.email, role: user.role },
      accessToken,
      refreshToken,
    }
  };
}

// Generate access token
function generateAccessToken(user: Pick<User, 'id' | 'email' | 'role'>): string {
  return jwt.sign(
    { userId: user.id, email: user.email, role: user.role } as JwtPayload,
    JWT_SECRET,
    { expiresIn: ACCESS_TOKEN_EXPIRY }
  );
}

// Generate refresh token
function generateRefreshToken(user: Pick<User, 'id' | 'email' | 'role'>): string {
  return jwt.sign(
    { userId: user.id, email: user.email, role: user.role } as JwtPayload,
    JWT_REFRESH_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRY }
  );
}

// Refresh access token using refresh token
export async function refreshAccessToken(refreshToken: string): Promise<{
  accessToken: string;
  refreshToken: string;
}> {
  try {
    const payload = jwt.verify(refreshToken, JWT_REFRESH_SECRET) as JwtPayload;
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { id: true, email: true, role: true, isActive: true },
    });

    if (!user || !user.isActive) {
      throw new Error('Invalid or inactive user');
    }

    const newAccessToken = generateAccessToken(user);
    const newRefreshToken = generateRefreshToken(user);

    return { accessToken: newAccessToken, refreshToken: newRefreshToken };
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
}

// TODO: Request password reset (e.g., send reset token via email)
export async function requestPasswordReset(email: string): Promise<string> {
  const user = await prisma.user.findUnique({ where: { email } });
  if (!user || !user.isActive) {
    throw new Error('User not found or inactive');
  }

  // Generate reset token (in production, send via email)
  const resetToken = jwt.sign(
    { userId: user.id, email: user.email },
    JWT_SECRET,
    { expiresIn: '1h' }
  );

  // store resetToken in a temporary table or cache with expiry
  // For simplicity, return it here
  return resetToken;
}

// Reset password using reset token
export async function resetPassword(resetToken: string, newPassword: string): Promise<void> {
  try {
    const payload = jwt.verify(resetToken, JWT_SECRET) as { userId: string };
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { id: true, isActive: true },
    });

    if (!user || !user.isActive) {
      throw new Error('Invalid or inactive user');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword },
    });
  } catch (error) {
    throw new Error('Invalid or expired reset token');
  }
}

// Verify email using verification token
export async function verifyEmail(verificationToken: string): Promise<ApiResponse<{ message: string }>> {
  try {
    const payload = jwt.verify(verificationToken, JWT_SECRET) as { userId: string };
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { id: true, isActive: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    if (user.isActive) {
      return {
        success: true,
        data: { message: 'Email already verified' },
      };
    }

    await prisma.user.update({
      where: { id: user.id },
      data: { isActive: true },
    });

    return {
      success: true,
      data: { message: 'Email verified successfully' },
    };
  } catch (error) {
    throw new Error('Invalid or expired verification token');
  }
}

// Verify admin
export async function verifyAdminWithToken(token: string): Promise<boolean> {
  try {
    const payload = jwt.verify(token, JWT_SECRET) as JwtPayload;
    console.log(payload)
    return payload.role === "admin";
  } catch (error) {
    console.error("Token verification failed:", error);
    return false;
  }
}

// Check if user has required role
export async function checkUserRole(userId: string, requiredRole: UserRole): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { role: true, isActive: true },
  });

  if (!user || !user.isActive) {
    return false;
  }

  return user.role === requiredRole;
}

// Get user profile
export async function getUserProfile(userId: string): Promise<ApiResponse<Pick<User, 'id' | 'name' | 'email' | 'role' | 'avatar' | 'phone' | 'lastLoginAt'>>> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      avatar: true,
      phone: true,
      lastLoginAt: true,
    },
  });

  if (!user) {
    throw new Error('User not found');
  }

  return {
    success: true,
    data: user,
  };
}

// Update user profile
export async function updateUserProfile(
  userId: string,
  updates: { name?: string; phone?: string; avatar?: string }
): Promise<ApiResponse<Pick<User, 'id' | 'name' | 'email' | 'role' | 'avatar' | 'phone'>>> {
  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      name: updates.name,
      phone: updates.phone,
      avatar: updates.avatar,
      updatedAt: new Date(),
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      avatar: true,
      phone: true,
    },
  });

  return {
    success: true,
    data: user,
  };
}