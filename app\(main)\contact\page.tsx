import { Mail, Phone, MapPin, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ContactPage() {
  return (
    <div className="min-h-screen overflow-hidden">
      <div className="container relative">
        {/* Header */}
        <section className="relative py-20">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5 backdrop-blur-sm">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.05),transparent_50%)]"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,105,180,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(255,105,180,0.05),transparent_50%)]"></div>
          </div>
          <div className="relative z-10 text-center space-y-6 mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 dark:bg-primary/5 border border-primary/20 backdrop-blur-sm">
              <Sparkles className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-primary">Get in Touch</span>
            </div>
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl bg-gradient-to-br from-foreground via-foreground/90 to-foreground/70 bg-clip-text text-transparent">
              Contact Us
            </h1>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl leading-relaxed">
              Have questions, suggestions, or want to partner with us? We'd love to hear from you.
            </p>
          </div>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 py-10 px-4 md:px-6 ">
          {/* Contact Form */}
          <Card className="flex flex-col border-0 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
            <CardHeader>
              <CardTitle className="text-xl bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                Send us a Message
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Fill out the form below and we'll get back to you as soon as possible.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow space-y-6">
              <form className="space-y-4 flex flex-col h-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-sm font-medium text-foreground">First Name</Label>
                    <Input
                      id="firstName"
                      placeholder="John"
                      className="rounded-xl border-border/50 hover:border-primary/50 transition-colors duration-300"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-sm font-medium text-foreground">Last Name</Label>
                    <Input
                      id="lastName"
                      placeholder="Doe"
                      className="rounded-xl border-border/50 hover:border-primary/50 transition-colors duration-300"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-foreground">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="rounded-xl border-border/50 hover:border-primary/50 transition-colors duration-300"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject" className="text-sm font-medium text-foreground">Subject</Label>
                  <Select>
                    <SelectTrigger className="rounded-xl border-border/50 hover:border-primary/50 transition-colors duration-300">
                      <SelectValue placeholder="Select a subject" />
                    </SelectTrigger>
                    <SelectContent className="bg-background/95 backdrop-blur-sm border-border/50">
                      <SelectItem value="general">General Inquiry</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="support">Technical Support</SelectItem>
                      <SelectItem value="feedback">Feedback</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-grow space-y-2">
                  <Label htmlFor="message" className="text-sm font-medium text-foreground">Message</Label>
                  <Textarea
                    id="message"
                    placeholder="Tell us how we can help you..."
                    className="min-h-[120px] h-full rounded-xl border-border/50 hover:border-primary/50 transition-colors duration-300"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 rounded-xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 group font-semibold"
                >
                  Send Message
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-6">
            <Card className="border-0 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <CardHeader>
                <CardTitle className="text-xl bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Contact Information
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Reach out to us through any of these channels.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {[
                  {
                    icon: Mail,
                    title: "Email",
                    value: "<EMAIL>",
                    gradient: "from-blue-500/10 to-blue-600/10 dark:from-blue-500/5 dark:to-blue-600/5",
                  },
                  {
                    icon: Phone,
                    title: "Phone",
                    value: "+****************",
                    gradient: "from-green-500/10 to-green-600/10 dark:from-green-500/5 dark:to-green-600/5",
                  },
                  {
                    icon: MapPin,
                    title: "Address",
                    value: "123 Business Avenue\nSuite 100\nCity, State 12345",
                    gradient: "from-purple-500/10 to-purple-600/10 dark:from-purple-500/5 dark:to-purple-600/5",
                  },
                  {
                    icon: Clock,
                    title: "Business Hours",
                    value: "Monday - Friday: 9:00 AM - 6:00 PM\nSaturday: 10:00 AM - 4:00 PM\nSunday: Closed",
                    gradient: "from-orange-500/10 to-orange-600/10 dark:from-orange-500/5 dark:to-orange-600/5",
                  },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="group flex items-start gap-3 p-4 rounded-2xl bg-background/80 backdrop-blur-sm hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-300"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className={`p-3 rounded-full bg-gradient-to-br ${item.gradient} group-hover:scale-110 transition-transform duration-300`}>
                      <item.icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground group-hover:text-primary transition-colors duration-300">
                        {item.title}
                      </p>
                      <p className="text-sm text-muted-foreground whitespace-pre-line">
                        {item.value}
                      </p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card className="border-0 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <CardHeader>
                <CardTitle className="text-xl bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Frequently Asked Questions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {[
                  {
                    question: "How do I add my mall to the platform?",
                    answer: "Contact us through the form or email us directly. We'll guide you through the process.",
                  },
                  {
                    question: "Is the service free for shoppers?",
                    answer: "Yes! MallSurf is completely free for shoppers to use.",
                  },
                  {
                    question: "How often is the information updated?",
                    answer: "We update store information regularly and work with mall partners to ensure accuracy.",
                  },
                ].map((faq, index) => (
                  <div
                    key={index}
                    className="group p-4 rounded-2xl bg-background/80 backdrop-blur-sm hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-300"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <h4 className="font-medium mb-1 text-foreground group-hover:text-primary transition-colors duration-300">
                      {faq.question}
                    </h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}