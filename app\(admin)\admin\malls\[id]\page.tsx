"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, Search, Filter, Edit, Trash2, Eye, MoreHorizontal } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialog<PERSON>itle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

const malls = [
  {
    id: 1,
    name: "Grand Central Plaza",
    location: "Downtown District",
    status: "Active",
    floors: 4,
    shops: 150,
    rating: 4.8,
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    name: "Riverside Mall",
    location: "Waterfront Area",
    status: "Active",
    floors: 3,
    shops: 120,
    rating: 4.6,
    createdAt: "2024-02-20",
  },
  {
    id: 3,
    name: "Tech Hub Center",
    location: "Innovation District",
    status: "Active",
    floors: 5,
    shops: 80,
    rating: 4.7,
    createdAt: "2024-03-10",
  },
  {
    id: 4,
    name: "Fashion District",
    location: "Style Avenue",
    status: "Draft",
    floors: 3,
    shops: 95,
    rating: 4.5,
    createdAt: "2024-03-25",
  },
  {
    id: 5,
    name: "Family Fun Center",
    location: "Suburban Plaza",
    status: "Active",
    floors: 2,
    shops: 110,
    rating: 4.4,
    createdAt: "2024-04-05",
  },
  {
    id: 6,
    name: "Outlet Paradise",
    location: "Highway Junction",
    status: "Inactive",
    floors: 2,
    shops: 200,
    rating: 4.3,
    createdAt: "2024-04-12",
  },
]

export default function MallsAdminPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("All")
  const [deleteId, setDeleteId] = useState<number | null>(null)

  const filteredMalls = malls.filter((mall) => {
    const matchesSearch = mall.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mall.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "All" || mall.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
      case "Inactive":
        return <Badge variant="secondary">Inactive</Badge>
      case "Draft":
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleDelete = (id: number) => {
    // Handle delete logic here
    console.log("Delete mall with id:", id)
    setDeleteId(null)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Malls Management</h1>
          <p className="text-muted-foreground">Manage all shopping malls in your system</p>
        </div>
        <Button asChild>
          <Link href="/admin/malls/new">
            <Plus className="mr-2 h-4 w-4" />
            Add Mall
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Malls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{malls.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Malls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{malls.filter(m => m.status === "Active").length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shops</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{malls.reduce((sum, mall) => sum + mall.shops, 0)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(malls.reduce((sum, mall) => sum + mall.rating, 0) / malls.length).toFixed(1)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search malls..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Status</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Malls Table */}
      <Card>
        <CardHeader>
          <CardTitle>Malls ({filteredMalls.length})</CardTitle>
          <CardDescription>A list of all malls in your system</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Floors</TableHead>
                <TableHead>Shops</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMalls.map((mall) => (
                <TableRow key={mall.id}>
                  <TableCell className="font-medium">{mall.name}</TableCell>
                  <TableCell>{mall.location}</TableCell>
                  <TableCell>{getStatusBadge(mall.status)}</TableCell>
                  <TableCell>{mall.floors}</TableCell>
                  <TableCell>{mall.shops}</TableCell>
                  <TableCell>{mall.rating}</TableCell>
                  <TableCell>{new Date(mall.createdAt).toLocaleDateString()}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/malls/${mall.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/malls/${mall.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/malls/${mall.id}/shops`}>
                            <Eye className="mr-2 h-4 w-4" />
                            Manage Shops
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-red-600"
                          onClick={() => setDeleteId(mall.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteId !== null} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the mall and all associated shops.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => deleteId && handleDelete(deleteId)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
