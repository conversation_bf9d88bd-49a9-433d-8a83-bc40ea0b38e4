
import { NextRequest, NextResponse } from 'next/server';
import { refreshAccessToken } from '@/lib/services/auth.service';
import { errorHandler } from '@/lib/api-error';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const result = await refreshAccessToken(body.refreshToken);
        (await cookies()).set({
            name: 'token',
            value: result.accessToken,
            path: '/',
            maxAge: 15 * 60 * 1000,
        });
        return NextResponse.json(result, { status: 200 });
    } catch (error) {
        return errorHandler(error);
    }
}
