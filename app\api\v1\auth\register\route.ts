
import { NextRequest, NextResponse } from 'next/server';
import { RegisterInput, registerUser } from '@/lib/services/auth.service';
import { validate } from '@/lib/middleware/validation';
import { errorHandler } from '@/lib/api-error';
import { UserCreateInputSchema } from '@/lib/generated/zod';

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as RegisterInput;
    const validatedData = await validate(UserCreateInputSchema, body);
    const result = await registerUser(validatedData);
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    return errorHandler(error);
  }
}
