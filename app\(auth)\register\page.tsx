"use client"
import React, { useState } from 'react';
import { AlertCircle, Eye, EyeOff, User, Mail, Lock, Phone } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import axios from 'axios';
import { RegisterInput } from '@/lib/services/auth.service';
import Link from 'next/link';
import { isValidEmail, isValidPassword, isValidPhoneNumber } from '@/lib';

// Validation function
const validateRegister = (data: RegisterInput) => {
    const allErrors: Partial<{ name?: string; email?: string; password?: string[]; phone?: string }> = {};

    // Password validation
    const { isValid, errors } = isValidPassword(data.password);
    if (!isValid) {
        allErrors.password = errors
    }

    // Email validation
    if (!data.email || !isValidEmail(data.email)) {
        allErrors.email = 'Please enter a valid email address';
    }

    // Name validation
    if (!data.name || data.name.length < 1) {
        allErrors.name = 'Name is required';
    }

    // Phone validation
    if (data.phone && !isValidPhoneNumber(data.phone)) {
        allErrors.phone = 'Please enter a valid 10-digit phone number';
    }

    return allErrors;
};

export default function RegisterPage() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        phone: '',
    });

    const [errors, setErrors] = useState<{ name?: string; email?: string; password?: string[]; phone?: string }>({});
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [submitMessage, setSubmitMessage] = useState('');

    const handleInputChange = (field: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error when user starts typing
        if ((errors as any)[field] ) {
            setErrors((prev: any) => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setSubmitMessage('');

        // Validate form
        const validationErrors = validateRegister(formData);
        setErrors(validationErrors);

        if (Object.keys(validationErrors).length > 0) {
            setIsLoading(false);
            return;
        }

        try {
            // Format data according to Prisma schema
            const userData = {
                name: formData.name,
                email: formData.email,
                password: formData.password,
                phone: formData.phone || null,
                isActive: true,
            };
            const response = await axios.post('/api/v1/auth/register', userData);

            setSubmitMessage('Account created successfully!');

            // Reset form
            setFormData({
                name: '',
                email: '',
                password: '',
                phone: '',
            });

        } catch (error) {
            setSubmitMessage('Failed to create account. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <Card>
                    <CardHeader className="text-center">
                        <CardTitle>Create Account</CardTitle>
                        <CardDescription>
                            Enter your information to create a new account
                        </CardDescription>
                    </CardHeader>

                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            {/* Name Field */}
                            <div className="space-y-2">
                                <Label htmlFor="name">
                                    Full Name <span className="text-red-500">*</span>
                                </Label>
                                <div className="relative">
                                    <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                                    <Input
                                        id="name"
                                        type="text"
                                        placeholder="Enter your full name"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        className={`pl-10 ${errors.name ? 'border-red-500' : ''}`}
                                    />
                                </div>
                                {errors.name && (
                                    <p className="text-sm text-red-500 flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        {errors.name}
                                    </p>
                                )}
                            </div>

                            {/* Email Field */}
                            <div className="space-y-2">
                                <Label htmlFor="email">
                                    Email Address <span className="text-red-500">*</span>
                                </Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        value={formData.email}
                                        onChange={(e) => handleInputChange('email', e.target.value)}
                                        className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                                    />
                                </div>
                                {errors.email && (
                                    <p className="text-sm text-red-500 flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        {errors.email}
                                    </p>
                                )}
                            </div>

                            {/* Password Field */}
                            <div className="space-y-2">
                                <Label htmlFor="password">
                                    Password <span className="text-red-500">*</span>
                                </Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Create a password"
                                        value={formData.password}
                                        onChange={(e) => handleInputChange('password', e.target.value)}
                                        className={`pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute right-3 top-3 h-4 w-4 text-slate-400 hover:text-slate-600"
                                    >
                                        {showPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {errors.password?.map((error: string, index: number) => (
                                    <p key={index} className="text-sm text-red-500 flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        {error}
                                    </p>
                                ))}
                            </div>

                            {/* Phone Field (Optional) */}
                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone Number (Optional)</Label>
                                <div className="relative">
                                    <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                                    <Input
                                        id="phone"
                                        type="tel"
                                        placeholder="Enter your phone number"
                                        value={formData.phone}
                                        onChange={(e) => handleInputChange('phone', e.target.value)}
                                        className={`pl-10 ${errors.phone ? 'border-red-500' : ''}`}
                                    />
                                </div>
                                {errors.phone && (
                                    <p className="text-sm text-red-500 flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        {errors.phone}
                                    </p>
                                )}
                            </div>

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <div className="flex items-center gap-2">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                        Creating Account...
                                    </div>
                                ) : (
                                    'Create Account'
                                )}
                            </Button>

                            {/* Success/Error Message */}
                            {submitMessage && (
                                <Alert variant={submitMessage.includes('successfully') ? 'default' : 'destructive'}>
                                    <AlertDescription>
                                        {submitMessage}
                                    </AlertDescription>
                                </Alert>
                            )}
                        </form>

                        {/* Sign In Link */}
                        <div className="mt-6 text-center">
                            <p className="text-sm text-slate-600">
                                Already have an account?{' '}
                                <Link href="/login" className="font-medium dark:text-white hover:underline">
                                    Sign in
                                </Link>
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}