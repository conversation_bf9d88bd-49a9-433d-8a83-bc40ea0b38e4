'use client'

import { motion } from "motion/react"
import { Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { ShopCardData } from "@/lib/types/mall"
import ShopCard, { ShopCardSkeleton } from "@/components/shop-card"
import { EmptyState } from "@/components/common/empty-state"
import { Store } from "lucide-react"

interface ShopsGridProps {
  shops: ShopCardData[]
  loading: boolean
  hasNextPage: boolean
  activeTab: string
  floors: string[]
  onLoadMore: () => void
}

export function ShopsGrid({
  shops,
  loading,
  hasNextPage,
  activeTab,
  floors,
  onLoadMore
}: ShopsGridProps) {
  if (loading && shops.length === 0) {
    return <ShopsGridSkeleton />
  }

  if (shops.length === 0) {
    return (
      <EmptyState
        icon={Store}
        title="No shops found"
        description="Try adjusting your filters or search terms to find what you're looking for."
        className="my-8"
      />
    )
  }

  if (activeTab === 'floor') {
    return <ShopsByFloor shops={shops} floors={floors} />
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {shops.map((shop, index) => (
          <motion.div
            key={shop.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <ShopCard shop={shop} url={`#`} />
          </motion.div>
        ))}
      </div>

      {/* Load More Button */}
      {hasNextPage && (
        <div className="flex justify-center pt-8">
          <Button
            onClick={onLoadMore}
            disabled={loading}
            variant="outline"
            size="lg"
            className="bg-background/50"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              'Load More Shops'
            )}
          </Button>
        </div>
      )}
    </div>
  )
}

function ShopsByFloor({ shops, floors }: { shops: ShopCardData[], floors: string[] }) {
  const shopsByFloor = floors
    .filter(floor => floor !== "All")
    .reduce((acc, floor) => {
      acc[floor] = shops.filter(shop => shop.floor?.toString() === floor)
      return acc
    }, {} as Record<string, ShopCardData[]>)

  return (
    <div className="space-y-12">
      {Object.entries(shopsByFloor).map(([floor, floorShops]) => {
        if (floorShops.length === 0) return null

        return (
          <motion.div
            key={floor}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-6"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-primary/10 to-secondary/10">
                <Store className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">Floor {floor}</h3>
                <p className="text-muted-foreground">{floorShops.length} shops</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {floorShops.map((shop, index) => (
                <motion.div
                  key={shop.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <ShopCard shop={shop} url="#" />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )
      })}
    </div>
  )
}

function ShopsGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: 12 }).map((_, index) => (
        <ShopCardSkeleton key={index} />
      ))}
    </div>
  )
}
