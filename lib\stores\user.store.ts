
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { getUserProfile, updateUserProfile } from '../api-functions'
import { User } from '../generated/prisma'
import { z } from 'zod'
import { UserUpdateInputSchema } from '../generated/zod'

interface UserState {
  // Data
  user: User | null

  // Loading states
  loading: boolean

  // Error states
  error: string | null

  // Actions
  setUser: (user: User | null) => void
  fetchUser: () => Promise<void>
  updateUser: (userData: z.infer<typeof UserUpdateInputSchema>) => Promise<void>
  logout: () => void
  clearError: () => void
}

const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      loading: false,
      error: null,

      // Actions
      setUser: (user) => set({
        user,
        error: null
      }),

      fetchUser: async () => {
        try {
          set({ loading: true, error: null })
          const user = await getUserProfile()
          set({
            user,
            loading: false
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to fetch user',
            loading: false
          })
        }
      },

      updateUser: async (userData) => {
        try {
          set({ loading: true, error: null })
          const updatedUser = await updateUserProfile(userData)
          set({
            user: updatedUser,
            loading: false
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update user',
            loading: false
          })
        }
      },

      logout: () => {
        set({
          user: null,
          error: null
        })
        // Clear token from localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('token')
          localStorage.removeItem('refresh_token')
        }
      },

      clearError: () => set({ error: null }),
    }),
    {
      name: 'user-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
      }),
    }
  )
)

export default useUserStore
