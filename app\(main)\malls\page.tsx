"use client"
import { unique } from 'radash'
import { useState, useEffect, useMemo, useCallback, useTransition } from "react"
import Link from "next/link"
import Image from "next/image"
import { Search, Filter, MapPin, Store, Star, Users, X, Loader2, Grid, List, SlidersHorizontal, Sparkles } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import axios from "axios"
import { ApiResponse } from "@/lib/types/api-response"
import { Mall } from "@/lib/generated/prisma"
import { GetMallsOptions } from "@/lib/types/mall"
import { Checkbox } from "@/components/ui/checkbox"
import { debounce } from 'radash'
import { getAmenities, getCategories } from '@/lib/api-functions'

interface MallData extends Mall {
  images: string[];
}

const formatFootfall = (footfall: number | null): string => {
  if (!footfall) return "N/A"
  if (footfall >= 1000000) {
    return `${(footfall / 1000000).toFixed(1)}M`
  }
  if (footfall >= 1000) {
    return `${(footfall / 1000).toFixed(0)}K`
  }
  return footfall.toString()
}

const getLocationString = (mall: MallData): string => {
  const parts = [mall.city, mall.state].filter(Boolean)
  return parts.join(", ") || "Location TBD"
}

// Loading skeleton component
const MallCardSkeleton = () => (
  <Card className="overflow-hidden">
    <div className="w-full h-48 bg-muted animate-pulse" />
    <CardHeader>
      <div className="h-6 bg-muted rounded animate-pulse mb-2" />
      <div className="h-4 bg-muted rounded w-2/3 animate-pulse" />
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="h-4 bg-muted rounded animate-pulse" />
      <div className="flex justify-between">
        <div className="h-4 bg-muted rounded w-20 animate-pulse" />
        <div className="h-4 bg-muted rounded w-20 animate-pulse" />
      </div>
      <div className="h-10 bg-muted rounded animate-pulse" />
    </CardContent>
  </Card>
)

export default function MallsPage() {
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [isPending, startTransition] = useTransition()
  const [malls, setMalls] = useState<MallData[]>([])
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [allCategories, setAllCategories] = useState<string[]>(['all'])
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [allAmenities, setAllAmenities] = useState<string[]>([])
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([])
  const [activeStatusFilter, setActiveStatusFilter] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("name")
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showMobileFilters, setShowMobileFilters] = useState(false)

  const handleAmenityChange = useCallback((amenity: string, checked: boolean) => {
    setSelectedAmenities(prev => 
      checked 
        ? [...prev, amenity]
        : prev.filter((a) => a !== amenity)
    )
  }, [])

  const clearAllFilters = useCallback(() => {
    setSearchTerm("")
    setSelectedCategory("all")
    setActiveStatusFilter("all")
    setSelectedAmenities([])
    setSortBy("name")
  }, [])

  // Optimized fetch function with error handling
  const fetchFilters = useCallback(async () => {
    if (allCategories.length > 1 && allAmenities.length > 0) return
    
    try {
      const [categories, amenities] = await Promise.all([
        getCategories(),
        getAmenities()
      ])
      
      setAllCategories(['all', ...unique(categories)])
      setAllAmenities(unique(amenities))
    } catch (error) {
      console.error("Error fetching filters:", error)
    }
  }, [allCategories.length, allAmenities.length])

  // Optimized fetch function for malls with better error handling
  const fetchMalls = useCallback(async (search: string) => {
    try {
      setIsLoading(true)

      const response = await axios.get("/api/v1/malls", {
        params: {
          page: 1,
          limit: 50, // Increased limit for better UX
          activeOnly: activeStatusFilter === "active",
          searchTerm: search,
          category: selectedCategory !== "all" ? selectedCategory : undefined,
          amenities: selectedAmenities.length > 0 ? selectedAmenities : undefined,
          sortBy,
        } as GetMallsOptions,
      })

      const data = response.data as ApiResponse<MallData[]>
      setMalls(data.data ?? [])
    } catch (error) {
      console.error("Error fetching malls:", error)
      setMalls([]) // Reset on error
    } finally {
      setIsLoading(false)
    }
  }, [selectedCategory, activeStatusFilter, selectedAmenities, sortBy])

  // Optimized debounced search
  const debouncedSearch = useMemo(
    () => debounce({ delay: 300 }, (search: string) => {
      startTransition(() => {
        fetchMalls(search)
      })
    }),
    [fetchMalls]
  )

  // Handle search input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    debouncedSearch(value)
  }, [debouncedSearch])

  // Filter change handlers with transitions
  const handleCategoryChange = useCallback((value: string) => {
    startTransition(() => {
      setSelectedCategory(value)
    })
  }, [])

  const handleStatusChange = useCallback((value: string) => {
    startTransition(() => {
      setActiveStatusFilter(value)
    })
  }, [])

  const handleSortChange = useCallback((value: string) => {
    startTransition(() => {
      setSortBy(value)
    })
  }, [])

  // Initial fetch and filter changes
  useEffect(() => {
    fetchMalls(searchTerm)
    fetchFilters()
  }, [selectedCategory, activeStatusFilter, selectedAmenities, sortBy, fetchMalls, fetchFilters, searchTerm])

  // Active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (selectedCategory !== "all") count++
    if (activeStatusFilter !== "all") count++
    if (selectedAmenities.length > 0) count++
    if (searchTerm) count++
    return count
  }, [selectedCategory, activeStatusFilter, selectedAmenities.length, searchTerm])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container px-4 md:px-6 py-8">
        {/* Enhanced Header */}
        <div className="space-y-6 mb-8">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-2xl bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-primary/5 dark:to-secondary/5">
              <Store className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                Shopping Malls
              </h1>
              <p className="text-muted-foreground text-lg mt-1">
                Discover amazing shopping destinations with diverse stores and experiences.
              </p>
            </div>
          </div>

          {/* Mobile Filter Toggle */}
          <div className="lg:hidden flex items-center justify-between">
            <Button
              variant="outline"
              onClick={() => setShowMobileFilters(!showMobileFilters)}
              className="flex items-center gap-2"
            >
              <SlidersHorizontal className="h-4 w-4" />
              Filters
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 flex items-center justify-center text-xs">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
            
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Enhanced Filters Sidebar */}
          <div className={`lg:col-span-1 space-y-6 ${showMobileFilters ? 'block' : 'hidden lg:block'}`}>
            <Card className="bg-gradient-to-br from-background to-muted/30 border-0 shadow-lg backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Filter className="h-5 w-5 text-primary" />
                    <span>Filters</span>
                  </div>
                  {activeFiltersCount > 0 && (
                    <Badge variant="secondary" className="bg-primary/10 text-primary">
                      {activeFiltersCount} active
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Enhanced Search */}
                <div className="space-y-2">
                  <Label className="font-medium">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search malls..."
                      value={searchTerm}
                      onChange={handleSearchChange}
                      className="pl-10 bg-background/50 border-muted-foreground/20 focus:bg-background transition-colors"
                    />
                    {searchTerm && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-1 top-1 h-8 w-8 p-0"
                        onClick={() => {
                          setSearchTerm("")
                          debouncedSearch("")
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                <Separator className="bg-muted-foreground/20" />

                {/* Enhanced Category Filter */}
                <div className="space-y-2">
                  <Label className="font-medium">Category</Label>
                  <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                    <SelectTrigger className="bg-background/50 border-muted-foreground/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {allCategories.map((category, index) => (
                        <SelectItem key={index} value={category}>
                          {category === "all" ? "All Categories" : category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Enhanced Status Filter */}
                <div className="space-y-2">
                  <Label className="font-medium">Status</Label>
                  <Select value={activeStatusFilter} onValueChange={handleStatusChange}>
                    <SelectTrigger className="bg-background/50 border-muted-foreground/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active Only</SelectItem>
                      <SelectItem value="inactive">Inactive Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator className="bg-muted-foreground/20" />

                {/* Enhanced Amenities Filter */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Amenities</Label>
                    {selectedAmenities.length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {selectedAmenities.length} selected
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-3 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/20">
                    {allAmenities.map((amenity, index) => (
                      <div key={index} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/30 transition-colors">
                        <Checkbox
                          id={amenity}
                          checked={selectedAmenities.includes(amenity)}
                          onCheckedChange={(checked) => handleAmenityChange(amenity, checked as boolean)}
                        />
                        <Label 
                          htmlFor={amenity} 
                          className="text-sm cursor-pointer flex-1"
                        >
                          {amenity}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="bg-muted-foreground/20" />

                {/* Enhanced Clear Filters */}
                <Button 
                  variant="outline" 
                  onClick={clearAllFilters} 
                  className="w-full bg-background/50 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20 transition-colors"
                  disabled={activeFiltersCount === 0}
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear All Filters
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Enhanced Sort and Results */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 rounded-2xl bg-gradient-to-r from-muted/30 to-muted/50 dark:from-muted/20 dark:to-muted/30 backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <p className="text-muted-foreground font-medium">
                  Showing <span className="text-foreground font-bold">{malls.length}</span> malls
                </p>
                {isPending && <Loader2 className="h-4 w-4 animate-spin text-primary" />}
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="bg-primary/10 text-primary">
                    {activeFiltersCount} filters active
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-4">
                {/* Desktop View Toggle */}
                <div className="hidden lg:flex items-center gap-1 p-1 rounded-lg bg-background/50">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="h-8 w-8 p-0"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="h-8 w-8 p-0"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="sort" className="font-medium">Sort:</Label>
                  <Select value={sortBy} onValueChange={handleSortChange}>
                    <SelectTrigger className="w-40 bg-background/50 border-muted-foreground/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name A-Z</SelectItem>
                      <SelectItem value="rating">Rating ⭐</SelectItem>
                      <SelectItem value="totalShops">Store Count</SelectItem>
                      <SelectItem value="totalFloors">Floors</SelectItem>
                      <SelectItem value="footfall">Footfall</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Enhanced Mall Grid/List */}
            {isLoading ? (
              <div className={`grid ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-1'} gap-6`}>
                {Array.from({ length: 6 }).map((_, i) => (
                  <MallCardSkeleton key={i} />
                ))}
              </div>
            ) : malls.length > 0 ? (
              <div className={`grid ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-1'} gap-6`}>
                {malls.map((mall, index) => (
                  <Card 
                    key={mall.id} 
                    className="group overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-background to-muted/20 border-0 backdrop-blur-sm"
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animation: 'fadeInUp 0.5s ease-out forwards'
                    }}
                  >
                    <div className="relative overflow-hidden">
                      <Image
                        src={mall.images[0] || "/placeholder.svg"}
                        alt={mall.name}
                        width={400}
                        height={250}
                        priority={index < 4}
                        className="w-full h-48 object-cover transition-all duration-500 group-hover:scale-110"
                      />
                      
                      {/* Enhanced overlays */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      
                      {/* Enhanced badges */}
                      <div className="absolute top-4 right-4 flex flex-col gap-2">
                        {(mall as any)._count?.floors && (
                          <Badge className="bg-primary/90 text-primary-foreground backdrop-blur-md shadow-lg">
                            {(mall as any)._count?.floors} Floors
                          </Badge>
                        )}
                        {mall.rating && (
                          <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white backdrop-blur-md shadow-lg flex items-center gap-1">
                            <Star className="h-3 w-3 fill-current" />
                            {mall.rating.toFixed(1)}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="absolute top-4 left-4">
                        <Badge className={`${mall.isActive 
                          ? "bg-gradient-to-r from-green-500 to-emerald-500" 
                          : "bg-gradient-to-r from-red-500 to-rose-500"
                        } text-white shadow-lg backdrop-blur-md`}>
                          {mall.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>

                      {/* Popular badge for high-traffic malls */}
                      {mall.averageFootFall && mall.averageFootFall > 10000 && (
                        <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-100">
                          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg backdrop-blur-md flex items-center gap-1">
                            <Sparkles className="h-3 w-3" />
                            Popular
                          </Badge>
                        </div>
                      )}
                    </div>
                    
                    <CardHeader className="pb-3">
                      <CardTitle className="group-hover:text-primary transition-colors line-clamp-1">
                        {mall.name}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-1 text-muted-foreground">
                        <MapPin className="h-4 w-4 flex-shrink-0" />
                        <span className="line-clamp-1">{getLocationString(mall)}</span>
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-4 pt-0">
                      <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                        {mall.shortDescription || "Discover amazing shopping experiences at this destination."}
                      </p>

                      {/* Enhanced stats */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="flex items-center gap-2 p-2 rounded-xl bg-muted/30 dark:bg-muted/20">
                          <div className="p-1.5 rounded-full bg-primary/10">
                            <Store className="h-3 w-3 text-primary" />
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Stores</p>
                            <p className="font-bold text-sm">{mall.totalShops || 0}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 p-2 rounded-xl bg-muted/30 dark:bg-muted/20">
                          <div className="p-1.5 rounded-full bg-secondary/10">
                            <Users className="h-3 w-3 text-secondary" />
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Visitors</p>
                            <p className="font-bold text-sm">{formatFootfall(mall.averageFootFall)}</p>
                          </div>
                        </div>
                      </div>

                      <Button 
                        asChild 
                        className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md hover:shadow-lg transition-all duration-300 group/btn"
                      >
                        <Link href={`/malls/${mall.slug}`}>
                          <span>View Details</span>
                          <Store className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="p-12 text-center bg-gradient-to-br from-muted/30 to-muted/50 border-0">
                <div className="space-y-4">
                  <div className="mx-auto w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center">
                    <Search className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">No malls found</h3>
                    <p className="text-muted-foreground mb-4">
                      No malls match your current search criteria. Try adjusting your filters.
                    </p>
                    <Button variant="outline" onClick={clearAllFilters}>
                      <X className="h-4 w-4 mr-2" />
                      Clear All Filters
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>

      <style jsx>{`
        .scrollbar-thin::-webkit-scrollbar {
          width: 4px;
        }
        
        .scrollbar-thumb-muted-foreground\/20::-webkit-scrollbar-thumb {
          background-color: hsl(var(--muted-foreground) / 0.2);
          border-radius: 2px;
        }
      `}</style>
    </div>
  )
}