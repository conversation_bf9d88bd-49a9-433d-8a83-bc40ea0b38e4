import { NextRequest, NextResponse } from 'next/server';
import { update<PERSON>all, deleteMall, getMallBySlug } from '@/lib/services/mall.service';
import { validate } from '@/lib/middleware/validation';
import { errorHandler } from '@/lib/api-error';
import {  MallUpdateInputSchema } from '@/lib/generated/zod';
import { authMiddleware } from '@/lib/middleware/auth';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const result = await getMallBySlug((await params).slug);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const validatedData = await validate(MallUpdateInputSchema, body);
    const result = await updateMall(params.id, validatedData);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await authMiddleware(request);
    if (user.role !== 'admin') {
      throw new Error('Unauthorized');
    }
    const result = await deleteMall((await params).slug);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}