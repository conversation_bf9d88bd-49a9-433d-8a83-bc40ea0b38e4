"use client"
import React, { useState } from 'react';
import { MapPin, Navigation, Maximize2, ExternalLink } from 'lucide-react';
import { createPortal } from 'react-dom';
import dynamic from 'next/dynamic';
const MapModal = dynamic(() => import("@/components/map-modal"), { ssr: false })

export default function Map({
  lat = 37.7749,
  lng = -122.4194,
  zoom = 15,
  popup = "You are here!",
  mallName = '',
  address = ''
}) {
  const [showMap, setShowMap] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;

  return (
    <>
      {/* Enhanced Location Card */}
      <div className="group relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5 opacity-50 group-hover:opacity-100 transition-opacity duration-500"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(120,119,198,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_20%_80%,rgba(120,119,198,0.03),transparent_50%)]"></div>

        {/* Floating Elements */}
        <div className="absolute top-3 right-6 w-1.5 h-1.5 bg-primary/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-4 left-8 w-1 h-1 bg-secondary/30 rounded-full animate-bounce delay-300"></div>

        <div className="relative border border-border/50 rounded-2xl p-6 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1">
          <div className="flex items-start justify-between gap-4">
            <div className="space-y-4 flex-1">
              {/* Header with Icon */}
              <div className="flex items-center gap-3">
                <div className="p-2.5 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/5 dark:to-primary/10 group-hover:from-primary/20 group-hover:to-primary/10 dark:group-hover:from-primary/10 dark:group-hover:to-primary/15 transition-all duration-300">
                  <MapPin className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold text-lg bg-gradient-to-br from-foreground to-foreground/80 bg-clip-text text-transparent">
                    Location
                  </h4>
                  {mallName && (
                    <p className="text-sm text-primary font-medium">{mallName}</p>
                  )}
                </div>
              </div>

              {/* Address */}
              <div className="space-y-2">
                <p className="text-muted-foreground leading-relaxed">
                  {address || popup}
                </p>
                <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-muted/50 dark:bg-muted/30">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-muted-foreground">
                    {lat.toFixed(4)}, {lng.toFixed(4)}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-3">
              <button
                onClick={() => setShowMap(true)}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                className="group/btn inline-flex items-center justify-center h-11 px-6 rounded-xl border-2 border-border/50 bg-background/80 hover:bg-primary/5 dark:hover:bg-primary/10 backdrop-blur-sm transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg text-sm font-medium"
              >
                <Maximize2 className="w-4 h-4 mr-2 transition-transform group-hover/btn:scale-110" />
                View Map
              </button>

              <a
                href={directionsUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="group/btn inline-flex items-center justify-center h-11 px-6 rounded-xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 text-sm font-medium"
              >
                <Navigation className="w-4 h-4 mr-2 transition-transform group-hover/btn:translate-x-0.5" />
                Directions
                <ExternalLink className="w-3 h-3 ml-1 opacity-70" />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Map Modal */}
      {showMap && createPortal(
        <MapModal
          open={showMap}
          onClose={() => setShowMap(false)}
          lat={lat}
          lng={lng}
          zoom={zoom}
          popup={popup}
          mallName={mallName}
          address={address}
        />,
        document.body
      )}
    </>
  );
}