import React, { useState, useEffect } from 'react';
import { MapPin, Navigation, ExternalLink, X, Maximize2 } from 'lucide-react';

export default function Map({ 
  lat = 37.7749, 
  lng = -122.4194, 
  zoom = 15, 
  popup = "You are here!",
  mallName = '',
  address = ''
}) {
  const [showMap, setShowMap] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [mapType, setMapType] = useState('roadmap');

  useEffect(() => {
    if (showMap) {
      const timer = setTimeout(() => setIsLoaded(true), 300);
      return () => clearTimeout(timer);
    }
  }, [showMap]);

  const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
  
  const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=${zoom}&size=800x600&maptype=${mapType}&markers=color:red%7Csize:mid%7C${lat},${lng}&style=feature:poi.business|visibility:simplified&style=feature:transit|visibility:off&key=AIzaSyC4R6AN7SmujjPUIGKdyao2Kqitzr1kiRg`;

  return (
    <>
      {/* Location Summary - Replaces the full map */}
      <div className="border rounded-lg p-4 bg-card">
        <div className="flex items-start justify-between gap-3">
          <div className="space-y-2 flex-1">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-primary" />
              <h4 className="font-medium">Location</h4>
            </div>
            <p className="text-sm text-muted-foreground">
              {address || popup}
            </p>
            <div className="text-xs text-muted-foreground">
              {lat.toFixed(4)}, {lng.toFixed(4)}
            </div>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => setShowMap(true)}
              className="inline-flex items-center justify-center h-8 px-3 rounded-md border bg-background hover:bg-muted transition-colors text-sm"
            >
              <Maximize2 className="w-3 h-3 mr-1" />
              View Map
            </button>
            <a
              href={directionsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center h-8 px-3 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors text-sm"
            >
              <Navigation className="w-3 h-3 mr-1" />
              Directions
            </a>
          </div>
        </div>
      </div>

      {/* Full Screen Map Modal */}
      {showMap && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm">
          <div className="fixed inset-4 bg-background rounded-lg shadow-xl overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div>
                <h3 className="font-semibold">{mallName || popup}</h3>
                <p className="text-sm text-muted-foreground">{address}</p>
              </div>
              <div className="flex items-center gap-2">
                {/* Map Type Toggle */}
                <div className="bg-muted rounded-md p-1">
                  <button
                    onClick={() => setMapType(mapType === 'roadmap' ? 'satellite' : 'roadmap')}
                    className="px-3 py-1 text-xs font-medium rounded hover:bg-background transition-colors"
                  >
                    {mapType === 'roadmap' ? '🛰️ Satellite' : '🗺️ Map'}
                  </button>
                </div>
                <button
                  onClick={() => {
                    setShowMap(false);
                    setIsLoaded(false);
                  }}
                  className="inline-flex items-center justify-center w-8 h-8 rounded-md hover:bg-muted transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Map Content */}
            <div className="relative flex-1 h-full">
              {!isLoaded ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center space-y-3">
                    <div className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                    <p className="text-sm text-muted-foreground">Loading map...</p>
                  </div>
                </div>
              ) : (
                <div className="relative h-full">
                  <img 
                    src={staticMapUrl}
                    alt={`Map showing ${popup}`}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Location Pin Overlay */}
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="relative">
                      <div className="w-4 h-4 bg-red-500 rounded-full animate-ping absolute opacity-75"></div>
                      <MapPin className="w-6 h-6 text-red-600 relative z-10 drop-shadow-lg" fill="currentColor" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="p-4 border-t bg-muted/30">
              <div className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground">
                  Click and drag to explore • {lat.toFixed(4)}, {lng.toFixed(4)}
                </div>
                <div className="flex gap-2">
                  <a
                    href={googleMapsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center h-9 px-4 rounded-md border bg-background hover:bg-muted transition-colors text-sm"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Open in Google Maps
                  </a>
                  <a
                    href={directionsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center h-9 px-4 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors text-sm"
                  >
                    <Navigation className="w-4 h-4 mr-2" />
                    Get Directions
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}