// Validation utilities

/**
 * Check if email is valid
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Check if phone number is valid
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  const cleaned = phone.replace(/\D/g, '')
  return phoneRegex.test(cleaned) && cleaned.length >= 10
}

/**
 * Check if URL is valid
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Check if password meets requirements
 */
export function isValidPassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Check if string is not empty or just whitespace
 */
export function isNotEmpty(value: string): boolean {
  return value.trim().length > 0
}

/**
 * Check if value is a valid number
 */
export function isValidNumber(value: any): boolean {
  return !isNaN(value) && !isNaN(parseFloat(value))
}

/**
 * Check if value is within range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max
}

/**
 * Check if file is valid image
 */
export function isValidImage(file: File): {
  isValid: boolean
  error?: string
} {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
  const maxSize = 5 * 1024 * 1024 // 5MB
  
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'File must be a valid image (JPEG, PNG, WebP, or GIF)',
    }
  }
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size must be less than 5MB',
    }
  }
  
  return { isValid: true }
}

/**
 * Check if coordinates are valid
 */
export function isValidCoordinates(lat: number, lng: number): boolean {
  return (
    isValidNumber(lat) &&
    isValidNumber(lng) &&
    isInRange(lat, -90, 90) &&
    isInRange(lng, -180, 180)
  )
}

/**
 * Check if time format is valid (HH:MM)
 */
export function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

/**
 * Check if date is valid and not in the past
 */
export function isValidFutureDate(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  
  return dateObj instanceof Date && !isNaN(dateObj.getTime()) && dateObj > now
}

/**
 * Sanitize string for safe display
 */
export function sanitizeString(str: string): string {
  return str
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .trim()
}

/**
 * Check if slug is valid (URL-friendly)
 */
export function isValidSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
  return slugRegex.test(slug)
}

/**
 * Generate a valid slug from text
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

/**
 * Validate form data against schema
 */
export function validateFormData<T extends Record<string, any>>(
  data: T,
  schema: Record<keyof T, (value: any) => boolean | { isValid: boolean; error: string }>
): {
  isValid: boolean
  errors: Partial<Record<keyof T, string>>
} {
  const errors: Partial<Record<keyof T, string>> = {}
  
  for (const [key, validator] of Object.entries(schema)) {
    const value = data[key as keyof T]
    const result = validator(value)
    
    if (typeof result === 'boolean') {
      if (!result) {
        errors[key as keyof T] = `${key} is invalid`
      }
    } else {
      if (!result.isValid) {
        errors[key as keyof T] = result.error
      }
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}
