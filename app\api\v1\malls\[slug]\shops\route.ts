import { NextResponse } from 'next/server';
import { getMallShops } from '@/lib/services/mall.service';
import { errorHandler } from '@/lib/api-error';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const searchTerm = searchParams.get('searchTerm') || '';
    const category = searchParams.get('category') || 'All';
    const floor = searchParams.get('floor') || 'All';
    
    const result = await getMallShops((await params).id, { page, limit, searchTerm, category, floor });
    return NextResponse.json(result);
    } catch (error) {
    return errorHandler(error);
  }
}

