// Mock data for the mall
const mallData = {
    1: {
        id: 1,
        name: "Grand Central Plaza",
        location: "123 Downtown Avenue, Downtown District",
        image: "/placeholder.svg?height=400&width=800",
        description:
            "Grand Central Plaza is the premier shopping destination in the heart of the downtown district. With over 150 stores across 4 floors, we offer an unparalleled shopping experience featuring luxury brands, fine dining, and world-class entertainment.",
        rating: 4.8,
        floors: 4,
        shopCount: 150,
        openingHours: "10:00 AM - 10:00 PM",
        phone: "+****************",
        amenities: ["Free Parking", "Food Court", "Cinema", "Kids Zone", "WiFi", "ATM", "Customer Service"],
        categories: ["Luxury", "Fashion", "Dining"],
        shops: [
            // Floor 1
            {
                id: 1,
                name: "Apple Store",
                category: "Electronics",
                floor: 1,
                description: "Latest Apple products and accessories",
                phone: "+****************",
                hours: "10:00 AM - 9:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 2,
                name: "<PERSON><PERSON>",
                category: "Fashion",
                floor: 1,
                description: "Trendy fashion for men and women",
                phone: "+****************",
                hours: "10:00 AM - 10:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 3,
                name: "Starbucks",
                category: "Food & Beverage",
                floor: 1,
                description: "Premium coffee and light snacks",
                phone: "+****************",
                hours: "6:00 AM - 10:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 4,
                name: "Nike",
                category: "Sports",
                floor: 1,
                description: "Athletic wear and sports equipment",
                phone: "+****************",
                hours: "10:00 AM - 9:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },

            // Floor 2
            {
                id: 5,
                name: "H&M",
                category: "Fashion",
                floor: 2,
                description: "Affordable fashion for the whole family",
                phone: "+****************",
                hours: "10:00 AM - 10:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 6,
                name: "Sephora",
                category: "Beauty",
                floor: 2,
                description: "Premium beauty products and cosmetics",
                phone: "+****************",
                hours: "10:00 AM - 9:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 7,
                name: "GameStop",
                category: "Electronics",
                floor: 2,
                description: "Video games and gaming accessories",
                phone: "+****************",
                hours: "10:00 AM - 9:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 8,
                name: "Bath & Body Works",
                category: "Beauty",
                floor: 2,
                description: "Fragrances and personal care products",
                phone: "+****************",
                hours: "10:00 AM - 9:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },

            // Floor 3
            {
                id: 9,
                name: "Food Court",
                category: "Food & Beverage",
                floor: 3,
                description: "Diverse dining options from around the world",
                phone: "+****************",
                hours: "10:00 AM - 10:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 10,
                name: "Target",
                category: "Department Store",
                floor: 3,
                description: "Everything you need for home and lifestyle",
                phone: "+****************",
                hours: "8:00 AM - 10:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 11,
                name: "Best Buy",
                category: "Electronics",
                floor: 3,
                description: "Consumer electronics and tech support",
                phone: "+****************",
                hours: "10:00 AM - 9:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },

            // Floor 4
            {
                id: 12,
                name: "Cinema Complex",
                category: "Entertainment",
                floor: 4,
                description: "Latest movies in premium theaters",
                phone: "+****************",
                hours: "10:00 AM - 12:00 AM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 13,
                name: "Kids Zone",
                category: "Entertainment",
                floor: 4,
                description: "Fun activities and games for children",
                phone: "+****************",
                hours: "10:00 AM - 8:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
            {
                id: 14,
                name: "Luxury Lounge",
                category: "Services",
                floor: 4,
                description: "VIP shopping assistance and relaxation area",
                phone: "+****************",
                hours: "10:00 AM - 8:00 PM",
                image: "/placeholder.svg?height=200&width=300",
            },
        ],
    },
};

const categories = [
  "All",
  "Fashion",
  "Electronics",
  "Food & Beverage",
  "Beauty",
  "Sports",
  "Entertainment",
  "Department Store",
  "Services",
]

const malls = [
  {
    id: 1,
    name: "Grand Central Plaza",
    location: "Downtown District",
    image: "/placeholder.svg?height=300&width=400",
    description: "Premier shopping destination with luxury brands and fine dining",
    shopCount: 150,
    floors: 4,
    rating: 4.8,
    categories: ["Luxury", "Fashion", "Dining"],
    amenities: ["Parking", "Food Court", "Cinema", "Kids Zone"],
  },
  {
    id: 2,
    name: "Riverside Mall",
    location: "Waterfront Area",
    image: "/placeholder.svg?height=300&width=400",
    description: "Family-friendly mall with entertainment and diverse shopping options",
    shopCount: 120,
    floors: 3,
    rating: 4.6,
    categories: ["Family", "Entertainment", "Fashion"],
    amenities: ["Parking", "Food Court", "Playground", "Cinema"],
  },
  {
    id: 3,
    name: "Tech Hub Center",
    location: "Innovation District",
    image: "/placeholder.svg?height=300&width=400",
    description: "Modern mall focused on technology, gadgets, and digital lifestyle",
    shopCount: 80,
    floors: 5,
    rating: 4.7,
    categories: ["Technology", "Electronics", "Gaming"],
    amenities: ["Parking", "Food Court", "Gaming Zone", "Tech Support"],
  },
  {
    id: 4,
    name: "Fashion District",
    location: "Style Avenue",
    image: "/placeholder.svg?height=300&width=400",
    description: "Trendy fashion mall with the latest brands and designer boutiques",
    shopCount: 95,
    floors: 3,
    rating: 4.5,
    categories: ["Fashion", "Luxury", "Beauty"],
    amenities: ["Parking", "Food Court", "Beauty Salon", "Personal Shopper"],
  },
  {
    id: 5,
    name: "Family Fun Center",
    location: "Suburban Plaza",
    image: "/placeholder.svg?height=300&width=400",
    description: "Perfect for families with kids' activities and diverse dining options",
    shopCount: 110,
    floors: 2,
    rating: 4.4,
    categories: ["Family", "Entertainment", "Dining"],
    amenities: ["Parking", "Food Court", "Kids Zone", "Playground", "Cinema"],
  },
  {
    id: 6,
    name: "Outlet Paradise",
    location: "Highway Junction",
    image: "/placeholder.svg?height=300&width=400",
    description: "Best deals and discounts on popular brands and designer items",
    shopCount: 200,
    floors: 2,
    rating: 4.3,
    categories: ["Outlet", "Fashion", "Sports"],
    amenities: ["Parking", "Food Court", "Customer Service"],
  },
]

const locations = [
  "All",
  "Downtown District",
  "Waterfront Area",
  "Innovation District",
  "Style Avenue",
  "Suburban Plaza",
  "Highway Junction",
]

const featuredMalls = [
  {
    id: 1,
    name: "Grand Central Plaza",
    location: "Downtown District",
    image: "/placeholder.svg?height=300&width=400",
    description: "Premier shopping destination with luxury brands and fine dining",
    shopCount: 150,
    floors: 4,
    categories: ["Luxury", "Fashion", "Dining"],
  },
  {
    id: 2,
    name: "Riverside Mall",
    location: "Waterfront Area",
    image: "/placeholder.svg?height=300&width=400",
    description: "Family-friendly mall with entertainment and diverse shopping options",
    shopCount: 120,
    floors: 3,
    categories: ["Family", "Entertainment", "Fashion"],
  },
  {
    id: 3,
    name: "Tech Hub Center",
    location: "Innovation District",
    image: "/placeholder.svg?height=300&width=400",
    description: "Modern mall focused on technology, gadgets, and digital lifestyle",
    shopCount: 80,
    floors: 5,
    categories: ["Technology", "Electronics", "Gaming"],
  },
]

export { mallData, categories, locations, malls };
