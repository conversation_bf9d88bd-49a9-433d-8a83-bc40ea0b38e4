import Link from "next/link"
import { Store, Mail, Phone, MapPin } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-muted/50 border-t">
      <div className="container px-4 md:px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <Store className="h-6 w-6 text-primary" />
              <span className="font-bold text-xl">MallSurf</span>
            </Link>
            <p className="text-sm text-muted-foreground">
              Your ultimate guide to discovering amazing shopping destinations and exploring the best stores in your
              area.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/" className="block text-sm text-muted-foreground hover:text-foreground">
                Home
              </Link>
              <Link href="/malls" className="block text-sm text-muted-foreground hover:text-foreground">
                All Malls
              </Link>
              <Link href="/about" className="block text-sm text-muted-foreground hover:text-foreground">
                About Us
              </Link>
              <Link href="/contact" className="block text-sm text-muted-foreground hover:text-foreground">
                Contact
              </Link>
            </div>
          </div>

          {/* Categories */}
          <div className="space-y-4">
            <h3 className="font-semibold">Categories</h3>
            <div className="space-y-2">
              <Link
                href="/malls?category=Fashion"
                className="block text-sm text-muted-foreground hover:text-foreground"
              >
                Fashion
              </Link>
              <Link
                href="/malls?category=Electronics"
                className="block text-sm text-muted-foreground hover:text-foreground"
              >
                Electronics
              </Link>
              <Link href="/malls?category=Dining" className="block text-sm text-muted-foreground hover:text-foreground">
                Dining
              </Link>
              <Link
                href="/malls?category=Entertainment"
                className="block text-sm text-muted-foreground hover:text-foreground"
              >
                Entertainment
              </Link>
            </div>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="font-semibold">Contact Us</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>123 Business Ave, City, State</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 mallsurf. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
