import { NextResponse } from 'next/server';
import { getShops, createShop } from '@/lib/services/shop.service';
import { validate } from '@/lib/middleware/validation';
import { errorHandler } from '@/lib/api-error';
import { ShopSchema } from '@/lib/generated/zod';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const mallId = searchParams.get('mallId');
    const floor = searchParams.get('floor');
    
    const result = await getShops({ page, limit, mallId, floor });
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = await validate(ShopSchema, body);
    const result = await createShop(validatedData);
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    return errorHandler(error);
  }
}

