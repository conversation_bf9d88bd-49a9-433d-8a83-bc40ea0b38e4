import { Store, MapPin, Users } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"

const features = [
  {
    icon: Store,
    title: "Comprehensive Directory",
    description: "Complete information about every store, including location, contact details, and operating hours.",
    gradient: "from-blue-500/10 to-blue-600/10 dark:from-blue-500/5 dark:to-blue-600/5"
  },
  {
    icon: MapPin,
    title: "Easy Navigation",
    description: "Interactive floor plans and detailed directions to help you find exactly what you're looking for.",
    gradient: "from-green-500/10 to-green-600/10 dark:from-green-500/5 dark:to-green-600/5"
  },
  {
    icon: Users,
    title: "Real-time Updates",
    description: "Stay informed with the latest store information, promotions, and special events happening at each mall.",
    gradient: "from-purple-500/10 to-purple-600/10 dark:from-purple-500/5 dark:to-purple-600/5"
  }
]

export function FeaturesSection() {
  return (
    <section className="py-24 relative">
      <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-background to-muted/20 dark:from-muted/20 dark:via-background dark:to-muted/10"></div>
      <div className="container px-4 md:px-6 relative z-10">
        <div className="text-center space-y-6 mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
            Why Choose Our Platform?
          </h2>
          <p className="mx-auto max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
            Everything you need to discover and explore the best shopping destinations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-3 border-0 bg-background/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-4">
                <div className={`mx-auto p-4 rounded-2xl bg-gradient-to-br ${feature.gradient} group-hover:scale-110 transition-transform duration-300 mb-4`}>
                  <feature.icon className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-xl group-hover:text-primary transition-colors duration-300">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
