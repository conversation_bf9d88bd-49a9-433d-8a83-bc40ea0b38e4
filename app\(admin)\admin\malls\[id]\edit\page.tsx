"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Plus, Trash2, Upload } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock data for the mall
const mallData = {
  id: 1,
  name: "Grand Central Plaza",
  description:
    "Grand Central Plaza is the premier shopping destination in the heart of the downtown district. With over 150 stores across 4 floors, we offer an unparalleled shopping experience featuring luxury brands, fine dining, and world-class entertainment.",
  location: "Downtown District",
  address: "123 Downtown Avenue, Downtown District",
  phone: "+****************",
  email: "<EMAIL>",
  website: "www.grandcentralplaza.com",
  openingHours: "10:00 AM - 10:00 PM",
  floors: 4,
  shopCount: 150,
  status: "active",
  featured: true,
  categories: ["Luxury", "Fashion", "Dining"],
  amenities: ["Free Parking", "Food Court", "Cinema", "Kids Zone", "WiFi", "ATM", "Customer Service"],
  image: "/placeholder.svg?height=400&width=800",
  gallery: [
    "/placeholder.svg?height=300&width=400",
    "/placeholder.svg?height=300&width=400",
    "/placeholder.svg?height=300&width=400",
    "/placeholder.svg?height=300&width=400",
  ],
  floorDetails: [
    { id: 1, name: "Ground Floor", description: "Main entrance, luxury brands, and food court" },
    { id: 2, name: "First Floor", description: "Fashion and accessories" },
    { id: 3, name: "Second Floor", description: "Electronics and home goods" },
    { id: 4, name: "Third Floor", description: "Entertainment and dining" },
  ],
}

export default function EditMallPage({ params }: { params: { id: string } }) {
  const [amenities, setAmenities] = useState<string[]>(mallData.amenities)
  const [newAmenity, setNewAmenity] = useState("")
  const [categories, setCategories] = useState<string[]>(mallData.categories)
  const [newCategory, setNewCategory] = useState("")
  const [floors, setFloors] = useState(mallData.floors)
  const [floorDetails, setFloorDetails] = useState(mallData.floorDetails)

  const handleAddAmenity = () => {
    if (newAmenity.trim() && !amenities.includes(newAmenity.trim())) {
      setAmenities([...amenities, newAmenity.trim()])
      setNewAmenity("")
    }
  }

  const handleRemoveAmenity = (amenity: string) => {
    setAmenities(amenities.filter((a) => a !== amenity))
  }

  const handleAddCategory = () => {
    if (newCategory.trim() && !categories.includes(newCategory.trim())) {
      setCategories([...categories, newCategory.trim()])
      setNewCategory("")
    }
  }

  const handleRemoveCategory = (category: string) => {
    setCategories(categories.filter((c) => c !== category))
  }

  const updateFloorCount = (count: number) => {
    const newCount = Math.max(1, count)
    setFloors(newCount)

    // Update floor details array
    if (newCount > floorDetails.length) {
      // Add new floors
      const newFloors = Array.from({ length: newCount - floorDetails.length }).map((_, i) => ({
        id: floorDetails.length + i + 1,
        name: `Floor ${floorDetails.length + i + 1}`,
        description: "",
      }))
      setFloorDetails([...floorDetails, ...newFloors])
    } else if (newCount < floorDetails.length) {
      // Remove excess floors
      setFloorDetails(floorDetails.slice(0, newCount))
    }
  }

  const updateFloorDetail = (index: number, field: "name" | "description", value: string) => {
    const updatedFloors = [...floorDetails]
    updatedFloors[index] = { ...updatedFloors[index], [field]: value }
    setFloorDetails(updatedFloors)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          {/* <Button variant="ghost" asChild className="mb-2">
            <Link href="/admin/malls">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Malls
            </Link>
          </Button> */}
          <h2 className="text-3xl font-bold tracking-tight">Edit Mall</h2>
          <p className="text-muted-foreground">Edit details for {mallData.name}.</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Save as Draft</Button>
          <Button>Update Mall</Button>
        </div>
      </div>

      <Tabs defaultValue="details" className="space-y-6">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="floors">Floors</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Edit the basic details about the mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Mall Name</Label>
                <Input id="name" defaultValue={mallData.name} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea id="description" defaultValue={mallData.description} className="min-h-[120px]" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" defaultValue={mallData.location} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Full Address</Label>
                  <Input id="address" defaultValue={mallData.address} />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" defaultValue={mallData.phone} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" defaultValue={mallData.email} />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input id="website" defaultValue={mallData.website} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="openingHours">Opening Hours</Label>
                  <Input id="openingHours" defaultValue={mallData.openingHours} />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
                <CardDescription>Add categories for this mall.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a category"
                    value={newCategory}
                    onChange={(e) => setNewCategory(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleAddCategory()}
                  />
                  <Button type="button" size="icon" onClick={handleAddCategory}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {categories.map((category) => (
                    <Badge key={category} variant="secondary" className="flex items-center gap-1">
                      {category}
                      <button
                        onClick={() => handleRemoveCategory(category)}
                        className="ml-1 text-muted-foreground hover:text-foreground"
                      >
                        <Trash2 className="h-3 w-3" />
                        <span className="sr-only">Remove {category}</span>
                      </button>
                    </Badge>
                  ))}
                  {categories.length === 0 && <p className="text-sm text-muted-foreground">No categories added yet.</p>}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Amenities</CardTitle>
                <CardDescription>Add amenities available at this mall.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add an amenity"
                    value={newAmenity}
                    onChange={(e) => setNewAmenity(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleAddAmenity()}
                  />
                  <Button type="button" size="icon" onClick={handleAddAmenity}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {amenities.map((amenity) => (
                    <Badge key={amenity} variant="secondary" className="flex items-center gap-1">
                      {amenity}
                      <button
                        onClick={() => handleRemoveAmenity(amenity)}
                        className="ml-1 text-muted-foreground hover:text-foreground"
                      >
                        <Trash2 className="h-3 w-3" />
                        <span className="sr-only">Remove {amenity}</span>
                      </button>
                    </Badge>
                  ))}
                  {amenities.length === 0 && <p className="text-sm text-muted-foreground">No amenities added yet.</p>}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="floors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Floor Configuration</CardTitle>
              <CardDescription>Configure the floors and their details.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="floorCount">Number of Floors</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="floorCount"
                    type="number"
                    min={1}
                    value={floors}
                    onChange={(e) => updateFloorCount(Number.parseInt(e.target.value) || 1)}
                    className="w-24"
                  />
                  <span className="text-sm text-muted-foreground">floors</span>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-6">
                {floorDetails.map((floor, index) => (
                  <div key={floor.id} className="space-y-4 p-4 border rounded-md">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Floor {index + 1}</h3>
                      <Badge variant="outline">
                        {index === 0 ? "12" : index === 1 ? "8" : index === 2 ? "10" : "5"} shops
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`floor-${index}-name`}>Floor Name</Label>
                        <Input
                          id={`floor-${index}-name`}
                          value={floor.name}
                          onChange={(e) => updateFloorDetail(index, "name", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`floor-${index}-description`}>Description</Label>
                        <Input
                          id={`floor-${index}-description`}
                          value={floor.description}
                          onChange={(e) => updateFloorDetail(index, "description", e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/malls/${mallData.id}/shops?floor=${index + 1}`}>Manage Shops</Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="media" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Featured Image</CardTitle>
              <CardDescription>The main image for this mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="border rounded-md p-2">
                  <Image
                    src={mallData.image || "/placeholder.svg"}
                    alt={mallData.name}
                    width={800}
                    height={400}
                    className="w-full h-auto object-cover rounded"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm">
                    Replace Image
                  </Button>
                  <Button variant="destructive" size="sm">
                    Remove
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Gallery Images</CardTitle>
              <CardDescription>Additional images for this mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {mallData.gallery.map((image, index) => (
                  <div key={index} className="relative group">
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`Gallery image ${index + 1}`}
                      width={400}
                      height={300}
                      className="w-full h-auto object-cover rounded-md"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-md">
                      <Button variant="destructive" size="icon" className="h-8 w-8">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center mt-4">
                <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground mb-1">Drag and drop images here or click to browse</p>
                <p className="text-xs text-muted-foreground">You can upload multiple images</p>
                <Button variant="outline" size="sm" className="mt-4">
                  Add More Images
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Status & Visibility</CardTitle>
              <CardDescription>Control the mall's status and visibility.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="status">Status</Label>
                  <p className="text-sm text-muted-foreground">Is this mall active?</p>
                </div>
                <Switch id="status" defaultChecked={mallData.status === "active"} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status-select">Status Type</Label>
                <Select defaultValue={mallData.status}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="coming-soon">Coming Soon</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="featured">Featured</Label>
                  <p className="text-sm text-muted-foreground">Show on homepage</p>
                </div>
                <Switch id="featured" defaultChecked={mallData.featured} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-destructive">Danger Zone</CardTitle>
              <CardDescription>Irreversible actions for this mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border border-destructive/20 rounded-md p-4">
                <h3 className="font-medium mb-2">Delete this mall</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Once you delete a mall, all of its data including shops and floor information will be permanently
                  removed. This action cannot be undone.
                </p>
                <Button variant="destructive">Delete Mall</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
