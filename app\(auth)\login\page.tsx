"use client"
import React, { useState } from 'react';
import { AlertCircle, Eye, EyeOff, Mail, Lock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Link from 'next/link';
import useUserStore from '@/lib/stores/user.store';
import { api, ApiResponse, isValidEmail, isValidPassword } from '@/lib';
import { useAuthStore } from '@/hooks';
import { User } from '@/lib/generated/prisma';

const { setAccessToken, setAuthenticated } = useAuthStore.getState()

interface LoginInput {
    email: string;
    password: string;
}

// Validation function
const validateLogin = (data: LoginInput) => {
    const allErrors: Partial<{ email?: string[]; password?: string[] }> = {};

    // Email validation
    if (!data.email || !isValidEmail(data.email)) {
        allErrors.email?.push('Please enter a valid email address');
    }

    // Password validation
    const { isValid, errors } = isValidPassword(data.password);
    if (!isValid) {
        allErrors.password = errors;
    }

    return allErrors;
};

export default function LoginPage() {
    const [formData, setFormData] = useState<LoginInput>({
        email: '',
        password: '',
    });

    const [errors, setErrors] = useState<{ email?: string[]; password?: string[] }>({});
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [submitMessage, setSubmitMessage] = useState('');

    const handleInputChange = (field: keyof LoginInput, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    const handleSubmit = async () => {
        setIsLoading(true);
        setSubmitMessage('');

        // Validate form
        const validationErrors = validateLogin(formData);
        setErrors(validationErrors);

        if (Object.keys(validationErrors).length > 0) {
            setIsLoading(false);
            return;
        }

        try {
            const userData = {
                email: formData.email,
                password: formData.password,
            };
            const data = (await api.post('/auth/login', userData)) as ApiResponse<{
                user: User;
                accessToken: string;
                refreshToken: string;
            }>

            setSubmitMessage('Login successful! Redirecting...');

            useUserStore.getState().setUser(data.data?.user || null);

            const accessToken = data.data?.accessToken || null;
            const refreshToken = data.data?.refreshToken || null;

            if (accessToken && refreshToken) {
                setAccessToken(accessToken);
                if (data.data?.user?.role === 'admin') {
                    setAuthenticated(true);
                    window.location.href = '/admin';
                }
            }
        } catch (error) {
            setSubmitMessage('Login failed. Please check your credentials.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !isLoading) {
            handleSubmit();
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <Card>
                    <CardHeader className="text-center">
                        <CardTitle className="text-3xl font-bold">Welcome Back</CardTitle>
                        <CardDescription>
                            Sign in to your account to continue
                        </CardDescription>
                    </CardHeader>

                    <CardContent>
                        <div className="space-y-4">
                            {/* Email Field */}
                            <div className="space-y-2">
                                <Label htmlFor="email">
                                    Email Address <span className="text-red-500">*</span>
                                </Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        value={formData.email}
                                        onChange={(e) => handleInputChange('email', e.target.value)}
                                        onKeyPress={handleKeyPress}
                                        className={`pl-10 ${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                                        autoComplete="email"
                                    />
                                </div>
                                {errors.email?.map((error, index) => (
                                    <p key={index} className="text-sm text-red-500 flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        {error}
                                    </p>
                                ))}
                            </div>

                            {/* Password Field */}
                            <div className="space-y-2">
                                <Label htmlFor="password">
                                    Password <span className="text-red-500">*</span>
                                </Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Enter your password"
                                        value={formData.password}
                                        onChange={(e) => handleInputChange('password', e.target.value)}
                                        onKeyPress={handleKeyPress}
                                        className={`pl-10 pr-10 ${errors.password ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                                        autoComplete="current-password"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute right-3 top-3 h-4 w-4 text-slate-400 hover:text-slate-600 transition-colors"
                                        aria-label={showPassword ? 'Hide password' : 'Show password'}
                                    >
                                        {showPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {errors.password?.map((error, index) => (
                                    <p key={index} className="text-sm text-red-500 flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        {error}
                                    </p>
                                ))}
                            </div>

                            {/* Forgot Password Link */}
                            {/* <div className="flex justify-end">
                <button 
                  type="button"
                  className="text-sm text-slate-600 hover:text-slate-900 hover:underline transition-colors"
                >
                  Forgot your password?
                </button>
              </div> */}

                            {/* Submit Button */}
                            <Button
                                onClick={handleSubmit}
                                className="w-full"
                                disabled={isLoading}
                                size="lg"
                            >
                                {isLoading ? (
                                    <div className="flex items-center gap-2">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                        Signing In...
                                    </div>
                                ) : (
                                    'Sign In'
                                )}
                            </Button>

                            {/* Success/Error Message */}
                            {submitMessage && (
                                <Alert variant={submitMessage.includes('successful') ? 'default' : 'destructive'}>
                                    <AlertDescription>
                                        {submitMessage}
                                    </AlertDescription>
                                </Alert>
                            )}
                        </div>

                        {/* Sign Up Link */}
                        <div className="mt-6 text-center">
                            <p className="text-sm text-slate-600">
                                Don't have an account?{' '}
                                <Link
                                    href="/register"
                                    className="font-medium dark:text-white hover:underline transition-colors"
                                >
                                    Sign up
                                </Link>
                            </p>
                        </div>

                        {/* Divider */}
                        {/* <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-slate-200" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="px-2 text-slate-500">Or continue with</span>
                </div>
              </div>
            </div> */}

                        {/* Social Login Buttons */}
                        {/* <div className="mt-6 grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => console.log('Google login')}
              >
                <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Google
              </Button>
              
              <Button
                variant="outline"
                className="w-full"
                onClick={() => console.log('GitHub login')}
              >
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                </svg>
                GitHub
              </Button>
            </div> */}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}