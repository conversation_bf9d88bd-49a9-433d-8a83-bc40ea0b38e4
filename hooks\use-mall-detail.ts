'use client'

import { useState, useCallback, useMemo } from 'react'
import { debounce } from 'radash'
import { useMallDetails, useMallShops } from './api/use-malls'

export function useMallDetail(slug: string) {
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [selectedFloor, setSelectedFloor] = useState('All')
  const [activeTab, setActiveTab] = useState('all')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [showMap, setShowMap] = useState(false)
  const [page, setPage] = useState(1)

  // Memoized query parameters to stabilize useMallShops
  const queryParams = useMemo(
    () => ({
      page,
      limit: 12,
      searchTerm: searchTerm || undefined,
      category: selectedCategory !== 'All' ? selectedCategory : undefined,
      floor: selectedFloor !== 'All' ? selectedFloor : undefined,
    }),
    [page, searchTerm, selectedCategory, selectedFloor]
  )

  // API hooks
  const { 
    data: mall, 
    loading: isMallLoading, 
    error: mallError, 
    refetch: refetchMall 
  } = useMallDetails(slug)

  const { 
    data: shops, 
    loading: isShopsLoading, 
    error: shopsError, 
    meta: shopsMeta,
    refetch: refetchShops 
  } = useMallShops(slug, queryParams)

  // Derived data
  const categories = useMemo(() => {
    if (!mall?.categories) return ['All']
    return ['All', ...mall.categories.map(c => c.name)]
  }, [mall?.categories])

  const floors = useMemo(() => {
    if (!mall?.floors) return ['All']
    return ['All', ...mall.floors.map(f => f.name || `Floor ${f.number}`)]
  }, [mall?.floors])

  const totalShops = shopsMeta?.total || 0
  const hasNextPage = shopsMeta?.hasNext || false

  // Active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (searchTerm) count++
    if (selectedCategory !== 'All') count++
    if (selectedFloor !== 'All') count++
    return count
  }, [searchTerm, selectedCategory, selectedFloor])

  // Debounced search handler
  const debouncedSetSearchTerm = useMemo(
    () =>
      debounce({ delay: 300 }, (value: string) => {
        setSearchTerm(value)
        setPage(1) // Reset to first page
      }),
    []
  )

  // Event handlers
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      debouncedSetSearchTerm(e.target.value)
    },
    [debouncedSetSearchTerm]
  )

  const handleCategoryChange = useCallback((category: string) => {
    setSelectedCategory(category)
    setPage(1) // Reset to first page
  }, [])

  const handleFloorChange = useCallback((floor: string) => {
    setSelectedFloor(floor)
    setPage(1) // Reset to first page
  }, [])

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab)
    setPage(1) // Reset to first page
  }, [])

  const handleToggleMobileFilters = useCallback(() => {
    setShowMobileFilters(prev => !prev)
  }, [])

  const handleResetFilters = useCallback(() => {
    setSearchTerm('')
    setSelectedCategory('All')
    setSelectedFloor('All')
    setActiveTab('all')
    setPage(1)
  }, [])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isShopsLoading) {
      setPage(prev => prev + 1)
    }
  }, [hasNextPage, isShopsLoading])

  const handleShowMap = useCallback(() => {
    setShowMap(true)
  }, [])

  const handleCloseMap = useCallback(() => {
    setShowMap(false)
  }, [])

  const handleRefresh = useCallback(() => {
    refetchMall()
    refetchShops()
  }, [refetchMall, refetchShops])

  return {
    // Data
    mall,
    shops,
    categories,
    floors,
    totalShops,
    hasNextPage,
    
    // Loading states
    isMallLoading,
    isShopsLoading,
    
    // Error states
    mallError,
    shopsError,
    
    // Filter states
    searchTerm,
    selectedCategory,
    selectedFloor,
    activeTab,
    showMobileFilters,
    showMap,
    activeFiltersCount,
    
    // Event handlers
    handleSearchChange,
    handleCategoryChange,
    handleFloorChange,
    handleTabChange,
    handleToggleMobileFilters,
    handleResetFilters,
    handleLoadMore,
    handleShowMap,
    handleCloseMap,
    handleRefresh,
  }
}