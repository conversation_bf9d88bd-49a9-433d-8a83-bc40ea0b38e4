import { create } from 'zustand'
import { getCategories } from '../api-functions'

interface CategoriesState {
  categories: string[]
  loading: boolean
  error: string | null

  // Actions
  fetchCategories: () => Promise<void>
  clearError: () => void
}

const useCategoriesStore = create<CategoriesState>((set, get) => ({
  categories: [],
  loading: false,
  error: null,

  fetchCategories: async () => {
    // Don't fetch if already loaded
    if (get().categories.length > 0) return

    try {
      set({ loading: true, error: null })

      const categories = await getCategories()

      set({
        categories: categories || [],
        loading: false
      })
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch categories',
        loading: false
      })
    }
  },

  clearError: () => set({ error: null }),
}))

export default useCategoriesStore
