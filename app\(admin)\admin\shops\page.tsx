"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, Search, Filter, Eye, Edit, Trash2, MoreHorizontal, Building2, Store } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// Table components inline
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

const shops = [
  {
    id: 1,
    name: "Apple Store",
    category: "Electronics",
    mall: "Grand Central Plaza",
    mallId: 1,
    floor: 1,
    status: "Active",
    phone: "+****************",
    image: "/placeholder.svg?height=100&width=150",
    rating: 4.9,
    description: "Premium electronics and accessories",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
  },
  {
    id: 2,
    name: "Zara",
    category: "Fashion",
    mall: "Grand Central Plaza",
    mallId: 1,
    floor: 1,
    status: "Active",
    phone: "+****************",
    image: "/placeholder.svg?height=100&width=150",
    rating: 4.7,
    description: "Contemporary fashion for all",
    createdAt: "2024-01-20",
    updatedAt: "2024-01-25",
  },
  {
    id: 3,
    name: "Nike Outlet",
    category: "Sports",
    mall: "Riverside Mall",
    mallId: 2,
    floor: 2,
    status: "Active",
    phone: "+****************",
    image: "/placeholder.svg?height=100&width=150",
    rating: 4.6,
    description: "Athletic wear and footwear",
    createdAt: "2024-02-10",
    updatedAt: "2024-02-15",
  },
  {
    id: 4,
    name: "Tech World",
    category: "Electronics",
    mall: "Tech Hub Center",
    mallId: 3,
    floor: 1,
    status: "Draft",
    phone: "+****************",
    image: "/placeholder.svg?height=100&width=150",
    rating: 4.5,
    description: "Latest gadgets and tech solutions",
    createdAt: "2024-03-05",
    updatedAt: "2024-03-10",
  },
  {
    id: 5,
    name: "Fashion Boutique",
    category: "Fashion",
    mall: "Fashion District",
    mallId: 4,
    floor: 2,
    status: "Inactive",
    phone: "+****************",
    image: "/placeholder.svg?height=100&width=150",
    rating: 4.3,
    description: "Exclusive designer collections",
    createdAt: "2024-03-15",
    updatedAt: "2024-03-20",
  },
]

const categories = ["All", "Electronics", "Fashion", "Sports", "Beauty", "Dining"]
const malls = ["All", "Grand Central Plaza", "Riverside Mall", "Tech Hub Center", "Fashion District"]
const statuses = ["All", "Active", "Inactive", "Draft"]

export default function ShopsAdminPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedMall, setSelectedMall] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [viewMode, setViewMode] = useState("grid")

  const filteredShops = shops.filter((shop) => {
    const matchesSearch = shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shop.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shop.mall.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "All" || shop.category === selectedCategory
    const matchesMall = selectedMall === "All" || shop.mall === selectedMall
    const matchesStatus = selectedStatus === "All" || shop.status === selectedStatus

    return matchesSearch && matchesCategory && matchesMall && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
      case "Inactive":
        return <Badge variant="secondary">Inactive</Badge>
      case "Draft":
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleDelete = (shopId: number) => {
    console.log("Delete shop:", shopId)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Shops Management</h1>
          <p className="text-muted-foreground">
            Manage all shops across all malls, their information, and settings.
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/shops/new">
            <Plus className="mr-2 h-4 w-4" />
            Add New Shop
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shops</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shops.length}</div>
            <p className="text-xs text-muted-foreground">Across all malls</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shops</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shops.filter(s => s.status === "Active").length}</div>
            <p className="text-xs text-muted-foreground">Currently operating</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length - 1}</div>
            <p className="text-xs text-muted-foreground">Different types</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(shops.reduce((sum, shop) => sum + shop.rating, 0) / shops.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">Out of 5.0</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search shops..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedMall} onValueChange={setSelectedMall}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Mall" />
              </SelectTrigger>
              <SelectContent>
                {malls.map((mall) => (
                  <SelectItem key={mall} value={mall}>
                    {mall}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                Grid
              </Button>
              <Button
                variant={viewMode === "table" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("table")}
              >
                Table
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground">
          Showing {filteredShops.length} of {shops.length} shops
        </p>
      </div>

      {/* Grid View */}
      {viewMode === "grid" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredShops.map((shop) => (
            <Card key={shop.id} className="overflow-hidden">
              <div className="relative">
                <img
                  src={shop.image || "/placeholder.svg"}
                  alt={shop.name}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute top-2 right-2">
                  {getStatusBadge(shop.status)}
                </div>
                <div className="absolute top-2 left-2">
                  <Badge variant="secondary" className="bg-white/90 text-black">
                    {shop.category}
                  </Badge>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {shop.name}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/malls/${shop.mallId}/shops/${shop.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Public
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/shops/${shop.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete the shop
                              and all associated data.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction 
                              onClick={() => handleDelete(shop.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardTitle>
                <CardDescription>
                  {shop.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Mall:</span>
                  <Link 
                    href={`/admin/malls/${shop.mallId}/shops`} 
                    className="font-medium text-primary hover:underline"
                  >
                    {shop.mall}
                  </Link>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Floor:</span>
                  <span className="font-medium">Floor {shop.floor}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Rating:</span>
                  <span className="font-medium">{shop.rating}/5</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Phone:</span>
                  <span className="font-medium">{shop.phone}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Table View */}
      {viewMode === "table" && (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="text-left p-4 font-medium">Shop</th>
                    <th className="text-left p-4 font-medium">Category</th>
                    <th className="text-left p-4 font-medium">Mall</th>
                    <th className="text-left p-4 font-medium">Floor</th>
                    <th className="text-left p-4 font-medium">Rating</th>
                    <th className="text-left p-4 font-medium">Status</th>
                    <th className="text-left p-4 font-medium">Updated</th>
                    <th className="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredShops.map((shop) => (
                    <tr key={shop.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <img
                            src={shop.image || "/placeholder.svg"}
                            alt={shop.name}
                            className="w-10 h-10 rounded object-cover"
                          />
                          <div>
                            <div className="font-medium">{shop.name}</div>
                            <div className="text-sm text-muted-foreground">{shop.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant="outline">{shop.category}</Badge>
                      </td>
                      <td className="p-4">
                        <Link 
                          href={`/admin/malls/${shop.mallId}/shops`} 
                          className="text-primary hover:underline"
                        >
                          {shop.mall}
                        </Link>
                      </td>
                      <td className="p-4">Floor {shop.floor}</td>
                      <td className="p-4">{shop.rating}/5</td>
                      <td className="p-4">{getStatusBadge(shop.status)}</td>
                      <td className="p-4">{new Date(shop.updatedAt).toLocaleDateString()}</td>
                      <td className="p-4 text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/malls/${shop.mallId}/shops/${shop.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Public
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/shops/${shop.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete the shop
                                    and all associated data.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => handleDelete(shop.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {filteredShops.length === 0 && (
        <div className="text-center py-12">
          <Store className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-muted-foreground text-lg">No shops found matching your criteria.</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              setSearchTerm("")
              setSelectedCategory("All")
              setSelectedMall("All")
              setSelectedStatus("All")
            }}
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}