
import { ApiError } from '../api-error';
import prisma from '../prisma';
import { ApiResponse } from '../types/api-response';
import { Floor } from '../generated/prisma';
import { FloorCreateInput, FloorUpdateInput } from '../types/floor';

export const getFloorsByMallId = async (mallId: string): Promise<ApiResponse<Floor[]>> => {
    try {
        const floors = await prisma.floor.findMany({
            where: { mallId },
        });
        return {
            success: true,
            data: floors,
        };
    } catch (error: any) {
        throw new ApiError(400, error.message);
    }
};

export const getFloorById = async (id: string): Promise<ApiResponse<Floor>> => {
    try {
        const floor = await prisma.floor.findUnique({
            where: { id },
        });
        if (!floor) {
            throw new ApiError(404, 'Floor not found');
        }
        return {
            success: true,
            data: floor,
        };
    } catch (error: any) {
        throw new ApiError(400, error.message);
    }
};

export const createFloor = async (id: string, input: FloorCreateInput): Promise<ApiResponse<Floor>> => {
    try {
        const floor = await prisma.floor.create({ data: { ...(input as Omit<Floor, 'mallId'>), mallId: id } });
        return {
            success: true,
            data: floor,
        };
    } catch (error: any) {
        throw new ApiError(400, error.message);
    }
};

export const updateFloor = async (id: string, input: FloorUpdateInput): Promise<ApiResponse<Floor>> => {
    try {
        const floor = await prisma.floor.update({ where: { id }, data: input });
        if (!floor) {
            throw new ApiError(404, 'Floor not found');
        }
        return {
            success: true,
            data: floor,
        };
    } catch (error: any) {
        throw new ApiError(400, error.message);
    }
};

export const deleteFloor = async (id: string): Promise<ApiResponse<null>> => {
    try {
        const floor = await prisma.floor.delete({ where: { id } });
        if (!floor) {
            throw new ApiError(404, 'Floor not found');
        }
        return {
            success: true,
            data: null,
            message: 'Floor deleted successfully',
        };
    } catch (error: any) {
        throw new ApiError(400, error.message);
    }
};



