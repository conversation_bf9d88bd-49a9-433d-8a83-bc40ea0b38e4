export function isMallOpen(timeRanges: string[], closedDays: string[]): boolean {
    // Get current date and time in user's timezone
    const now = new Date();
    
    // Get current day name
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' });
    
    // If current day is in closedDays, return false
    if (closedDays.includes(currentDay)) {
        return false;
    }

    // Parse time ranges and check if current time falls within any range
    for (const range of timeRanges) {
        const [start, end] = range.split(' - ');
        
        // Create Date objects for comparison
        const currentDate = new Date();
        const startDate = new Date(currentDate.toDateString() + ' ' + start);
        const endDate = new Date(currentDate.toDateString() + ' ' + end);
        
        // Handle cases where end time is on the next day
        if (endDate < startDate) {
            endDate.setDate(endDate.getDate() + 1);
        }
        
        // Compare using Date objects
        if (currentDate >= startDate && currentDate <= endDate) {
            return true;
        }
    }

    return false;
}
