'use client'

import { Search, SlidersHorizontal, X, Grid, Building } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ShopFiltersProps {
  searchTerm: string
  selectedCategory: string
  selectedFloor: string
  activeTab: string
  categories: string[]
  floors: string[]
  showMobileFilters: boolean
  activeFiltersCount: number
  onSearchChange: (value: string) => void
  onCategoryChange: (value: string) => void
  onFloorChange: (value: string) => void
  onTabChange: (value: string) => void
  onToggleMobileFilters: () => void
  onResetFilters: () => void
}

export function ShopFilters({
  searchTerm,
  selectedCategory,
  selectedFloor,
  activeTab,
  categories,
  floors,
  showMobileFilters,
  activeFiltersCount,
  onSearchChange,
  onCategoryChange,
  onFloorChange,
  onTabChange,
  onToggleMobileFilters,
  onResetFilters,
}: ShopFiltersProps) {
  return (
    <>
      {/* Mobile Filter Toggle & Clear Filters */}
      <div className="flex items-center gap-3 justify-end">
        <div className="lg:hidden">
          <Button
            variant="outline"
            onClick={onToggleMobileFilters}
            className="flex items-center gap-2 bg-background/50"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 flex items-center justify-center text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </div>

        {activeFiltersCount > 0 && (
          <Button variant="outline" size="sm" onClick={onResetFilters} className="bg-background/50">
            <X className="h-4 w-4 mr-2" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Filter Panel */}
      <Card className={`bg-gradient-to-br from-background to-muted/30 border-0 shadow-lg backdrop-blur-sm ${showMobileFilters ? 'block' : 'hidden lg:block'}`}>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Label className="text-sm font-medium mb-2 block">Search Stores</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search stores..."
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="pl-10 bg-background/50 border-muted-foreground/20"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Category</Label>
              <Select value={selectedCategory} onValueChange={onCategoryChange}>
                <SelectTrigger className="bg-background/50 border-muted-foreground/20">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === "All" ? "All Categories" : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Floor Filter */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Floor</Label>
              <Select value={selectedFloor} onValueChange={onFloorChange}>
                <SelectTrigger className="bg-background/50 border-muted-foreground/20">
                  <SelectValue placeholder="All Floors" />
                </SelectTrigger>
                <SelectContent>
                  {floors.map(floor => (
                    <SelectItem key={floor} value={floor}>
                      {floor === "All" ? "All Floors" : `Floor ${floor}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* View Toggle */}
            <div className="flex items-end">
              <div className="w-full space-y-2">
                <Label className="text-sm font-medium">View</Label>
                <div className="flex gap-1 p-1 rounded-lg bg-background/50">
                  <Button
                    variant={activeTab === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => onTabChange('grid')}
                    className="flex-1"
                  >
                    <Grid className="h-4 w-4 mr-1" />
                    Grid
                  </Button>
                  <Button
                    variant={activeTab === 'floor' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => onTabChange('floor')}
                    className="flex-1"
                  >
                    <Building className="h-4 w-4 mr-1" />
                    Floor
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}
