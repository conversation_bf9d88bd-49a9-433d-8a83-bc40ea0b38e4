import Link from "next/link"
import { Arrow<PERSON><PERSON>, Store, Users, MapPin, Star, Sparkles } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function AboutPage() {
  return (
    <div className="min-h-screen overflow-hidden">
      <div className="container relative">
        {/* Hero Section */}
        <section className="relative py-20">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5 backdrop-blur-sm">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.05),transparent_50%)]"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,105,180,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(255,105,180,0.05),transparent_50%)]"></div>
          </div>
          
          <div className="relative z-10 text-center space-y-6 mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 dark:bg-primary/5 border border-primary/20 backdrop-blur-sm">
              <Sparkles className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-primary">About Us</span>
            </div>
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl bg-gradient-to-br from-foreground via-foreground/90 to-foreground/70 bg-clip-text text-transparent">
              About MallSurf
            </h1>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl leading-relaxed">
              We're passionate about connecting people with amazing shopping experiences and helping them discover the best retail destinations.
            </p>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20 px-4 md:px-12  relative">
          <div className="absolute inset-0 bg-gradient-to-r from-muted/30 to-muted/50 dark:from-muted/20 dark:to-muted/30"></div>
          <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                Our Mission
              </h2>
              <p className="text-muted-foreground leading-relaxed md:text-lg">
                At MallSurf, we believe that shopping should be an enjoyable and effortless experience. Our mission is to provide comprehensive information about shopping malls and their stores, making it easier for people to discover new places, find what they're looking for, and make the most of their shopping trips.
              </p>
              <p className="text-muted-foreground leading-relaxed md:text-lg">
                We're committed to creating a platform that serves both shoppers and businesses, fostering connections and helping local economies thrive through better discovery and engagement.
              </p>
            </div>
            <div className="space-y-6">
              <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                What We Offer
              </h2>
              <div className="space-y-6">
                {[
                  {
                    icon: Store,
                    title: "Comprehensive Mall Directory",
                    description: "Detailed information about shopping malls, including store listings, floor plans, and amenities.",
                    gradient: "from-blue-500/10 to-blue-600/10 dark:from-blue-500/5 dark:to-blue-600/5",
                  },
                  {
                    icon: MapPin,
                    title: "Easy Navigation",
                    description: "Interactive maps and clear directions to help you find stores and navigate malls efficiently.",
                    gradient: "from-green-500/10 to-green-600/10 dark:from-green-500/5 dark:to-green-600/5",
                  },
                  {
                    icon: Users,
                    title: "Community Driven",
                    description: "Real reviews and ratings from shoppers to help you make informed decisions.",
                    gradient: "from-purple-500/10 to-purple-600/10 dark:from-purple-500/5 dark:to-purple-600/5",
                  },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="group flex items-start gap-3 p-4 rounded-2xl bg-background/80 backdrop-blur-sm hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-300 transform hover:-translate-y-1"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className={`p-3 rounded-full bg-gradient-to-br ${item.gradient} group-hover:scale-110 transition-transform duration-300`}>
                      <item.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1 text-foreground group-hover:text-primary transition-colors duration-300">
                        {item.title}
                      </h3>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 px-4 md:px-6 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-background to-muted/20 dark:from-muted/20 dark:via-background dark:to-muted/10"></div>
          <div className="relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { value: "25+", label: "Partner Malls", icon: Store },
                { value: "2,500+", label: "Listed Stores", icon: Users },
                { value: "10,000+", label: "Monthly Users", icon: MapPin },
                { value: "4.8", label: "Average Rating", icon: Star },
              ].map((stat, index) => (
                <div
                  key={index}
                  className="group text-center transform transition-all duration-300 hover:-translate-y-2"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <Card className="border-0 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardContent className="pt-8 pb-6">
                      <div className="flex justify-center mb-4">
                        <div className="p-3 rounded-full bg-primary/10 dark:bg-primary/5 group-hover:bg-primary/20 dark:group-hover:bg-primary/10 transition-colors duration-300">
                          <stat.icon className="w-6 h-6 text-primary" />
                        </div>
                      </div>
                      <div className="text-4xl font-bold bg-gradient-to-br from-primary to-primary/70 bg-clip-text text-transparent mb-2">
                        {stat.value}
                      </div>
                      <p className="text-muted-foreground font-medium">{stat.label}</p>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-24 px-4 md:px-6 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-background to-secondary/5"></div>
          <div className="relative z-10 space-y-8">
            <div className="text-center space-y-6 mb-16">
              <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                Our Values
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto md:text-xl leading-relaxed">
                These core values guide everything we do and help us create the best possible experience for our users.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: Star,
                  title: "Quality First",
                  description: "We're committed to providing accurate, up-to-date information and maintaining the highest standards in everything we do.",
                  gradient: "from-blue-500/10 to-blue-600/10 dark:from-blue-500/5 dark:to-blue-600/5",
                },
                {
                  icon: Users,
                  title: "User-Centric",
                  description: "Our users are at the heart of every decision we make. We listen, learn, and continuously improve based on their feedback.",
                  gradient: "from-green-500/10 to-green-600/10 dark:from-green-500/5 dark:to-green-600/5",
                },
                {
                  icon: MapPin,
                  title: "Innovation",
                  description: "We embrace new technologies and creative solutions to make shopping discovery easier and more enjoyable.",
                  gradient: "from-purple-500/10 to-purple-600/10 dark:from-purple-500/5 dark:to-purple-600/5",
                },
              ].map((value, index) => (
                <Card
                  key={index}
                  className="group border-0 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-3"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <CardHeader className="text-center pb-4">
                    <div className={`mx-auto p-4 rounded-2xl bg-gradient-to-br ${value.gradient} group-hover:scale-110 transition-transform duration-300 mb-4`}>
                      <value.icon className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle className="text-xl group-hover:text-primary transition-colors duration-300">
                      {value.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 md:px-6 relative text-center">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-secondary/10 backdrop-blur-sm"></div>
          <div className="relative z-10 space-y-6">
            <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
              Ready to Explore?
            </h2>
            <p className="text-muted-foreground md:text-xl leading-relaxed max-w-2xl mx-auto">
              Discover the best shopping malls and start your next adventure with MallSurf.
            </p>
            <Button
              asChild
              size="lg"
              className="text-lg px-8 py-6 rounded-xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <Link href="/malls">
                Explore Malls
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </section>
      </div>
    </div>
  )
}