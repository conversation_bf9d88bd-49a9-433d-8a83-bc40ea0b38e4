
import { NextRequest, NextResponse } from 'next/server';
import { errorHandler } from '@/lib/api-error';
import { createFloor, getFloorsByMallId } from '@/lib/services/floor.service';
import { authMiddleware } from '@/lib/middleware/auth';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const result = await getFloorsByMallId((await params).id);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await authMiddleware(request);
    if (user.role !== 'admin') {
      throw new Error('Unauthorized');
    }
    const body = await request.json();
    const result = await createFloor((await params).id, body);
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    return errorHandler(error);
  }
}
