import axios from 'axios';

export async function refreshClientUserToken() {
  try {
    console.log("Refreshing token")
    const refreshToken = document.cookie.split('; ').find((row) => row.startsWith('refreshToken='))?.split('=')[1];
    if (!refreshToken) {
      throw new Error('No refresh token found');
    }

    await axios.post('/api/v1/auth/refresh-token', { refreshToken })
  } catch (error) {
    console.error('Failed to refresh token:', error);
  }
}
