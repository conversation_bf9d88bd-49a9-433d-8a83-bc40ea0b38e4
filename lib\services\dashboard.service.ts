import { startOfMonth, subMonths } from 'date-fns';
import prisma from '../prisma';
import { Mall, Report, User } from '../generated/prisma';

// Helper to get current and previous month's start dates
const getMonthRanges = () => {
  const currentMonthStart = startOfMonth(new Date());
  const previousMonthStart = startOfMonth(subMonths(new Date(), 1));
  return { currentMonthStart, previousMonthStart };
};

// 1. Total malls and difference from last month
export async function getTotalMallsAndDifference(): Promise<{
  total: number;
  difference: number;
}> {
  const { currentMonthStart, previousMonthStart } = getMonthRanges();

  const [currentCount, previousCount] = await Promise.all([
    prisma.mall.count({
      where: { createdAt: { gte: currentMonthStart }, isActive: true },
    }),
    prisma.mall.count({
      where: {
        createdAt: { gte: previousMonthStart, lt: currentMonthStart },
        isActive: true,
      },
    }),
  ]);

  return {
    total: currentCount,
    difference: currentCount - previousCount,
  };
}

// 2. Total shops and difference from last month
export async function getTotalShopsAndDifference(): Promise<{
  total: number;
  difference: number;
}> {
  const { currentMonthStart, previousMonthStart } = getMonthRanges();

  const [currentCount, previousCount] = await Promise.all([
    prisma.shop.count({
      where: { createdAt: { gte: currentMonthStart }, isActive: true },
    }),
    prisma.shop.count({
      where: {
        createdAt: { gte: previousMonthStart, lt: currentMonthStart },
        isActive: true,
      },
    }),
  ]);

  return {
    total: currentCount,
    difference: currentCount - previousCount,
  };
}

// 3. Total users and difference from last month
export async function getTotalUsersAndDifference(): Promise<{
  total: number;
  difference: number;
}> {
  const { currentMonthStart, previousMonthStart } = getMonthRanges();

  const [currentCount, previousCount] = await Promise.all([
    prisma.user.count({
      where: { createdAt: { gte: currentMonthStart } },
    }),
    prisma.user.count({
      where: {
        createdAt: { gte: previousMonthStart, lt: currentMonthStart },
      },
    }),
  ]);

  return {
    total: currentCount,
    difference: currentCount - previousCount,
  };
}

// 4. Total active users and percentage difference
export async function getTotalActiveUsersAndPercentage(): Promise<{
  total: number;
  percentageDifference: number;
}> {
  const { currentMonthStart, previousMonthStart } = getMonthRanges();

  const [currentCount, previousCount] = await Promise.all([
    prisma.user.count({
      where: { isActive: true, createdAt: { gte: currentMonthStart } },
    }),
    prisma.user.count({
      where: {
        isActive: true,
        createdAt: { gte: previousMonthStart, lt: currentMonthStart },
      },
    }),
  ]);

  const percentageDifference = previousCount
    ? ((currentCount - previousCount) / previousCount) * 100
    : 0;

  return {
    total: currentCount,
    percentageDifference: Number(percentageDifference.toFixed(2)),
  };
}

// 5. Total page views and percentage difference
export async function getTotalPageViewsAndPercentage(): Promise<{
  total: number;
  percentageDifference: number;
}> {
  const { currentMonthStart, previousMonthStart } = getMonthRanges();

  const [currentCount, previousCount] = await Promise.all([
    prisma.pageView.count({
      where: { viewedAt: { gte: currentMonthStart } },
    }),
    prisma.pageView.count({
      where: {
        viewedAt: { gte: previousMonthStart, lt: currentMonthStart },
      },
    }),
  ]);

  const percentageDifference = previousCount
    ? ((currentCount - previousCount) / previousCount) * 100
    : 0;

  return {
    total: currentCount,
    percentageDifference: Number(percentageDifference.toFixed(2)),
  };
}

// Overview: Recent malls (name, createdAt and city)
export async function getRecentMalls(limit: number = 5): Promise<
  Pick<Mall, 'name' | 'city'>[]
> {
  return prisma.mall.findMany({
    where: { isActive: true },
    select: { name: true, city: true, createdAt: true },
    orderBy: { createdAt: 'desc' },
    take: limit,
  });
}

// Overview: Recent users (name, createdAt and email)
export async function getRecentUsers(limit: number = 5): Promise<
  Pick<User, 'name' | 'email'>[]
> {
  return prisma.user.findMany({
    select: { name: true, email: true, createdAt: true },
    orderBy: { createdAt: 'desc' },
    take: limit,
  });
}

// Overview: Recent shops (name, createdAt and mall name)
export async function getRecentShops(limit: number = 5): Promise<
  { name: string; mallName: string }[]
> {
  return prisma.shop.findMany({
    where: { isActive: true },
    select: {
      name: true,
      createdAt: true,
      mall: { select: { name: true } },
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
  }).then(shops =>
    shops.map(shop => ({
      name: shop.name,
      mallName: shop.mall.name,
      createdAt: shop.createdAt,
    }))
  );
}

// Reports: Fetch recent reports
export async function getRecentReports(limit: number = 5): Promise<
  Pick<Report, 'id' | 'title' | 'type' | 'createdAt'>[]
> {
  return prisma.report.findMany({
    select: {
      id: true,
      title: true,
      type: true,
      createdAt: true,
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
  });
}

// Placeholder for analytics section (to be implemented later)
export async function getAnalyticsData(): Promise<{ message: string }> {
  return { message: 'Analytics section not yet implemented' };
}