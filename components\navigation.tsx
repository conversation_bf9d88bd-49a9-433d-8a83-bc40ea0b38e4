"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Store, Moon, Sun, Menu } from "lucide-react"
import { useState } from "react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { useAuthStore } from "@/hooks"

const navigation = [
  { name: "Home", href: "/" },
  { name: "Malls", href: "/malls" },
  { name: "About", href: "/about" },
  { name: "Contact", href: "/contact" },
]

export function Navigation() {
  const pathname = usePathname()
  const { isAuthenticated } = useAuthStore()
  const { theme, setTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full bg-gradient-to-br from-background via-background to-muted/30 border-b border-border/50 backdrop-blur-md supports-[backdrop-filter]:bg-background/95 shadow-lg">
      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />

      <div className="container relative flex h-16 items-center justify-between px-4 md:px-6">
        {/* Logo */}
        <Link href="/" className="group flex items-center space-x-2 transform transition-transform duration-300 hover:-translate-y-0.5">
          <span className="font-bold text-xl transition-colors">Mallsurf</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {isAuthenticated && (
            <Link
              href={'/admin'}
              className={`relative text-sm font-medium transition-all duration-300 transform hover:-translate-y-0.5 hover:text-primary group ${pathname === '/admin' ? "text-primary" : "text-muted-foreground"
                }`}
              style={{ animationDelay: `${0 * 100}ms` }}
            >
              Admin
              <div className={`absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-primary to-primary/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${pathname === '/admin' ? "opacity-100" : ""
                }`} />
            </Link>
          )}
          {navigation.map((item, index) => (
            <Link
              key={item.name}
              href={item.href}
              className={`relative text-sm font-medium transition-all duration-300 transform hover:-translate-y-0.5 hover:text-primary group ${pathname === item.href ? "text-primary" : "text-muted-foreground"
                }`}
              style={{ animationDelay: `${(isAuthenticated ? index - 1 : index) * 100}ms` }}
            >
              {item.name}
              <div className={`absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-primary to-primary/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${pathname === item.href ? "opacity-100" : ""
                }`} />
            </Link>
          ))}
        </nav>

        {/* Theme Toggle & Mobile Menu */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "light" ? "dark" : "light")}
            className="relative rounded-2xl bg-muted/30 dark:bg-muted/20 hover:bg-primary/10 dark:hover:bg-primary/10 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg"
          >
            <Sun className="h-5 w-5 rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0 text-primary" />
            <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100 text-primary" />
            <span className="sr-only">Toggle theme</span>
          </Button>

          {/* Mobile Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                className="relative rounded-2xl bg-muted/30 dark:bg-muted/20 hover:bg-primary/10 dark:hover:bg-primary/10 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                <Menu className="h-5 w-5 text-primary" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px] bg-gradient-to-br from-background/95 via-background to-muted/20 backdrop-blur-sm border-l border-border/50">
              <div className="flex flex-col space-y-6 mt-8">
                {navigation.map((item, index) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsOpen(false)}
                    className={`relative text-lg font-medium transition-all duration-300 transform hover:-translate-y-0.5 hover:text-primary group ${pathname === item.href ? "text-primary" : "text-muted-foreground"
                      }`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {item.name}
                    <div className={`absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-primary to-primary/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${pathname === item.href ? "opacity-100" : ""
                      }`} />
                  </Link>
                ))}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
    </header>
  )
}