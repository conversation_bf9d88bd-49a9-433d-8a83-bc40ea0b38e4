import { ToastProvider } from "@/components/ui/toast"
import { HeroSection } from "@/components/home/<USER>"
import { StatsSection } from "@/components/home/<USER>"
import { FeaturedMallsSection } from "@/components/home/<USER>"
import { FeaturesSection } from "@/components/home/<USER>"
import { getFeaturedMalls } from '@/lib/services/mall.service';

export default async function HomePageZustand() {
  const malls = (await getFeaturedMalls({ page: 1, limit: 6, isActiveOnly: false })).data

  return (
    <div className="min-h-screen overflow-hidden">
      <HeroSection />
      <StatsSection />
      <FeaturedMallsSection featuredMalls={malls ?? []} />
      <FeaturesSection />
      <ToastProvider />
    </div>
  )
}
