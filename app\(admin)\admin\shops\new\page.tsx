"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Upload, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

const malls = [
  { id: 1, name: "Grand Central Plaza", floors: 4 },
  { id: 2, name: "Riverside Mall", floors: 3 },
  { id: 3, name: "Tech Hub Center", floors: 5 },
  { id: 4, name: "Fashion District", floors: 3 },
  { id: 5, name: "Family Fun Center", floors: 2 },
  { id: 6, name: "Outlet Paradise", floors: 2 },
]

const categories = [
  "Fashion",
  "Electronics",
  "Food & Beverage",
  "Beauty",
  "Sports",
  "Entertainment",
  "Department Store",
  "Services",
  "Books & Media",
  "Home & Garden",
  "Jewelry",
  "Toys & Games",
]

const servicesList = [
  "Technical Support",
  "Device Setup",
  "Data Transfer",
  "Repairs",
  "Workshops",
  "Personal Shopping",
  "Gift Wrapping",
  "Delivery",
  "Installation",
  "Consultation",
]

export default function NewShopPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const preselectedMallId = searchParams.get("mallId")

  const [formData, setFormData] = useState({
    id: 0,
    mallId: "", // ObjectId as string
    name: "",
    description: "",
    phone: "",
    email: "",
    website: "",
    hours: {}, // Can be replaced with better default structure if known
    image: "",
    logo: "",
    featured: false,
    specialFeatures: [""], // Start with one empty field
    socialMedia: {
      facebook: "",
      instagram: "",
      twitter: "",
    },
    location: {
      floor: 0,
      wing: "",
      coordinates: "", // You can make it more structured if you're storing lat,lng
    },
    rating: 0, // default to 0, validate between 1 and 5 when submitting
    category: "",
    tags: [""],
    openingDate: "", // Can be a string for input type="date"
    isActive: true,
  })
  const [images, setImages] = useState<string[]>([])
  const [selectedMall, setSelectedMall] = useState<(typeof malls)[0] | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (formData.mallId) {
      const mall = malls.find((m) => m.id.toString() === formData.mallId)
      setSelectedMall(mall || null)
    }
  }, [formData.mallId])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    console.log("Form data:", formData)
    console.log("Images:", images)
    router.push("/admin/shops")
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const newImages = Array.from(files).map((file, index) =>
        `/placeholder.svg?height=200&width=300&text=Image${images.length + index + 1}`
      )
      setImages(prev => [...prev, ...newImages])
    }
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        services: [...prev.services, service],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        services: prev.services.filter((s) => s !== service),
      }))
    }
  }

  const addProduct = () => {
    const product = prompt("Enter product name:")
    if (product && product.trim()) {
      setFormData((prev) => ({
        ...prev,
        products: [...prev.products, product.trim()],
      }))
    }
  }

  const removeProduct = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      products: prev.products.filter((_, i) => i !== index),
    }))
  }

  const addFeature = () => {
    const feature = prompt("Enter feature name:")
    if (feature && feature.trim()) {
      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, feature.trim()],
      }))
    }
  }

  const removeFeature = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }))
  }

  const addImage = () => {
    const newImage = `/placeholder.svg?height=300&width=400&text=Shop${images.length + 1}`
    setImages([...images, newImage])
  }

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        {/* <Button variant="ghost" asChild>
          <Link href="/admin/shops">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Shops
          </Link>
        </Button> */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Shop</h1>
          <p className="text-muted-foreground">Create a new shop entry</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Enter the basic details of the shop</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Shop Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter shop name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="mall">Mall *</Label>
                    <Select
                      value={formData.mallId}
                      onValueChange={(value) => setFormData((prev) => ({ ...prev, mallId: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select mall" />
                      </SelectTrigger>
                      <SelectContent>
                        {malls.map((mall) => (
                          <SelectItem key={mall.id} value={mall.id.toString()}>
                            {mall.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="floor">Floor *</Label>
                    <Select
                      value={formData.floor.toString()}
                      onValueChange={(value) => setFormData((prev) => ({ ...prev, floor: Number.parseInt(value) }))}
                      disabled={!selectedMall}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select floor" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedMall &&
                          Array.from({ length: selectedMall.floors }, (_, i) => i + 1).map((floor) => (
                            <SelectItem key={floor} value={floor.toString()}>
                              Floor {floor}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Short Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of the shop"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fullDescription">Full Description</Label>
                  <Textarea
                    id="fullDescription"
                    value={formData.fullDescription}
                    onChange={(e) => setFormData((prev) => ({ ...prev, fullDescription: e.target.value }))}
                    placeholder="Detailed description of the shop and its offerings"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Draft">Draft</SelectItem>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>How customers can reach the shop</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                      placeholder="+****************"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => setFormData((prev) => ({ ...prev, website: e.target.value }))}
                      placeholder="https://www.shop.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="hours">Operating Hours</Label>
                    <Input
                      id="hours"
                      value={formData.hours}
                      onChange={(e) => setFormData((prev) => ({ ...prev, hours: e.target.value }))}
                      placeholder="10:00 AM - 9:00 PM"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Products & Features</CardTitle>
                <CardDescription>What the shop offers</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Label>Products</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addProduct}>
                      Add Product
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.products.map((product, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {product}
                        <X className="h-3 w-3 cursor-pointer" onClick={() => removeProduct(index)} />
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Label>Features</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addFeature}>
                      Add Feature
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.features.map((feature, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {feature}
                        <X className="h-3 w-3 cursor-pointer" onClick={() => removeFeature(index)} />
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Services</CardTitle>
                <CardDescription>Select services offered</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {servicesList.map((service) => (
                    <div key={service} className="flex items-center space-x-2">
                      <Checkbox
                        id={`service-${service}`}
                        checked={formData.services.includes(service)}
                        onCheckedChange={(checked) => handleServiceChange(service, checked as boolean)}
                      />
                      <Label htmlFor={`service-${service}`} className="text-sm">
                        {service}
                      </Label>
                    </div>
                  ))}
                </div>
                {formData.services.length > 0 && (
                  <div className="mt-4 pt-4 border-t">
                    <p className="text-sm font-medium mb-2">Selected ({formData.services.length}):</p>
                    <div className="flex flex-wrap gap-1">
                      {formData.services.slice(0, 3).map((service) => (
                        <Badge key={service} variant="outline" className="text-xs">
                          {service}
                        </Badge>
                      ))}
                      {formData.services.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{formData.services.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Images</CardTitle>
                <CardDescription>
                  Upload images of the mall. The first image will be used as the main image.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
                  <div className="space-y-2">
                    <Label htmlFor="images" className="cursor-pointer">
                      <span className="text-sm font-medium text-primary hover:text-primary/80">
                        Click to upload images
                      </span>
                      <Input
                        id="images"
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, GIF up to 10MB each
                    </p>
                  </div>
                </div>

                {images.length > 0 && (
                  <div className="space-y-3">
                    <p className="text-sm font-medium">Uploaded Images:</p>
                    <div className="grid grid-cols-2 gap-3">
                      {images.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image || "/placeholder.svg"}
                            alt={`Mall image ${index + 1}`}
                            className="w-full h-24 object-cover rounded border"
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute top-1 right-1 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="h-3 w-3" />
                          </button>
                          {index === 0 && (
                            <Badge className="absolute bottom-1 left-1 text-xs">
                              Main
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? "Creating Shop..." : "Create Shop"}
                </Button>
                <Button type="button" variant="outline" className="w-full" asChild>
                  <Link href="/admin/malls">Cancel</Link>
                </Button>
              </CardContent>
            </Card>

            {selectedMall && (
              <Card>
                <CardHeader>
                  <CardTitle>Mall Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">Mall:</span> {selectedMall.name}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Total Floors:</span> {selectedMall.floors}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Selected Floor:</span> {formData.floor}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </form>
    </div>
  )
}
