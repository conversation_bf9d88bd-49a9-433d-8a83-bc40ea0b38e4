'use client'

import Link from "next/link"
import { ArrowLeft, Store, Loader2, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useParams } from "next/navigation"
import dynamic from "next/dynamic"
import { motion } from "motion/react"
import { createPortal } from "react-dom"

// Components
import { MallHeader } from "@/components/mall/mall-header"
import { ShopFilters } from "@/components/mall/shop-filters"
import { ShopsGrid } from "@/components/mall/shops-grid"
import { LoadingPage } from "@/components/common/loading-spinner"
import { ErrorBoundary, DefaultErrorFallback } from "@/components/common/error-boundary"
import { useMallDetail } from "@/hooks/use-mall-detail"

// Dynamic imports
const MapModal = dynamic(() => import("@/components/map-modal"), { ssr: false })

export default function MallDetailPage() {
  const params = useParams<{ slug: string }>()
  const slug = params?.slug || ''

  const {
    // Data
    mall,
    shops,
    categories,
    floors,
    totalShops,
    hasNextPage,

    // Loading states
    isMallLoading,
    isShopsLoading,

    // Error states
    mallError,
    shopsError,

    // Filter states
    searchTerm,
    selectedCategory,
    selectedFloor,
    activeTab,
    showMobileFilters,
    showMap,
    activeFiltersCount,

    // Event handlers
    handleSearchChange,
    handleCategoryChange,
    handleFloorChange,
    handleTabChange,
    handleToggleMobileFilters,
    handleResetFilters,
    handleLoadMore,
    handleShowMap,
    handleCloseMap,
  } = useMallDetail(slug)

  // Refresh handler
  const handleRefresh = () => {
   
  }

  // Loading state
  if (isMallLoading) {
    return <LoadingPage text="Loading mall details..." />
  }

  // Error state
  if (mallError || !mall) {
    return (
      <DefaultErrorFallback
        error={new Error(mallError || 'Mall not found')}
        resetError={() => { }}
      />
    )
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container px-4 md:px-6 py-8">
          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-between mb-8"
          >
            <Button variant="ghost" asChild className="hover:bg-muted/50 group">
              <Link href="/malls">
                <ArrowLeft className="mr-2 h-4 w-4 transition-transform group-hover:-translate-x-1" />
                Back to Malls
              </Link>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isMallLoading || isShopsLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${(isMallLoading || isShopsLoading) ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </motion.div>

          {/* Mall Header */}
          <MallHeader
            mall={mall}
            totalShops={totalShops}
            onShowMap={handleShowMap}
          />

          <Separator className="my-12 bg-muted-foreground/20" />

          {/* Shops Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-8"
          >
            {/* Section Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-2xl bg-gradient-to-br from-primary/10 to-secondary/10">
                  <Store className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                    Stores & Shops
                  </h2>
                  <p className="text-muted-foreground">
                    Discover {totalShops} amazing stores across {floors.length - 1} floors
                  </p>
                </div>
              </div>
            </div>

            {/* Filters */}
            <ShopFilters
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
              selectedFloor={selectedFloor}
              activeTab={activeTab}
              categories={categories as string[] || []}
              floors={floors as string[] || []}
              showMobileFilters={showMobileFilters}
              activeFiltersCount={activeFiltersCount}
              onSearchChange={() => null}
              onCategoryChange={handleCategoryChange}
              onFloorChange={handleFloorChange}
              onTabChange={handleTabChange}
              onToggleMobileFilters={handleToggleMobileFilters}
              onResetFilters={handleResetFilters}
            />

            {/* Results Summary */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 rounded-2xl bg-gradient-to-r from-muted/30 to-muted/50 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <p className="text-muted-foreground font-medium">
                  Showing <span className="text-foreground font-bold">{shops.length}</span> of <span className="text-foreground font-bold">{totalShops}</span> stores
                </p>
                {isShopsLoading && <Loader2 className="h-4 w-4 animate-spin text-primary" />}
                {activeFiltersCount > 0 && (
                  <span className="text-sm text-muted-foreground">
                    ({activeFiltersCount} filter{activeFiltersCount > 1 ? 's' : ''} applied)
                  </span>
                )}
              </div>
            </div>

            {/* Shops Grid */}
            <ShopsGrid
              shops={shops}
              loading={isShopsLoading}
              hasNextPage={hasNextPage}
              activeTab={activeTab}
              floors={floors as string[] || []}
              onLoadMore={handleLoadMore}
            />
          </motion.div>
        </div>

        {/* Map Modal */}
        {showMap && mall && mall.lat && mall.lng && typeof window !== 'undefined' && createPortal(
          <MapModal
            open={showMap}
            lat={mall.lat}
            lng={mall.lng}
            zoom={15}
            popup={mall.name}
            mallName={mall.name}
            address={mall.address || ''}
            onClose={handleCloseMap} />,
          document.body
        )}
      </div>
    </ErrorBoundary>
  )
}
