"use client"
import React, { useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserIcon, Mail, Phone, Shield, Clock, Calendar, AlertCircle, Settings } from 'lucide-react';
import { useUserStore } from '@/hooks';
import { User } from '@/lib/generated/prisma';

const ProfilePage: React.FC = () => {
  const { user: profile, loading, error } = useUserStore();

  const formatDate = useCallback((dateString?: string | Date) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return isNaN(date.getTime())
      ? 'Invalid date'
      : new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        }).format(date);
  }, []);

  const roleConfig = useMemo(() => ({
    admin: { variant: 'destructive' as const, icon: Shield, gradient: 'from-red-500 to-red-600' },
    manager: { variant: 'default' as const, icon: Shield, gradient: 'from-blue-500 to-blue-600' },
    user: { variant: 'secondary' as const, icon: UserIcon, gradient: 'from-green-500 to-green-600' },
    default: { variant: 'outline' as const, icon: UserIcon, gradient: 'from-gray-500 to-gray-600' },
  }), []);

  const getRoleConfig = useCallback((role: string) => {
    return roleConfig[role as keyof typeof roleConfig] || roleConfig.default;
  }, [roleConfig]);

  // Loading skeleton
  if (loading) {
    return (
      <Card className="border-0 shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-64" />
              <Skeleton className="h-5 w-20" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="p-3 rounded-lg border">
                <Skeleton className="h-3 w-16 mb-2" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive" className="border-0">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // No profile data
  if (!profile) {
    return (
      <Alert className="border-0">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>No profile data available.</AlertDescription>
      </Alert>
    );
  }

  const roleInfo = getRoleConfig(profile.role);
  const RoleIcon = roleInfo.icon;

  return (
    <Card className="border-0 shadow-lg h-full w-full flex flex-col bg-inherit">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-4">
          {/* Avatar */}
          <div className="relative">
            {profile.avatar ? (
              <img
                src={profile.avatar}
                alt={`${profile.name}'s avatar`}
                className="w-16 h-16 rounded-full object-cover border-2 border-muted shadow-md"
                loading="lazy"
              />
            ) : (
              <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${roleInfo.gradient} flex items-center justify-center shadow-md`}>
                <UserIcon className="w-8 h-8 text-white" />
              </div>
            )}
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-background rounded-full" />
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="min-w-0 flex-1">
                <h2 className="text-xl font-semibold truncate">{profile.name}</h2>
                <p className="text-sm text-muted-foreground truncate">{profile.email}</p>
                <div className="mt-2">
                  <Badge variant={roleInfo.variant} className="gap-1 px-2 py-1 text-xs">
                    <RoleIcon className="w-3 h-3" />
                    {profile.role.charAt(0).toUpperCase() + profile.role.slice(1)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 flex-1 flex flex-col">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
          {/* Contact Info */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Mail className="w-4 h-4" />
              Contact
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <div className="min-w-0 flex-1">
                  <p className="text-xs text-muted-foreground">Email</p>
                  <p className="text-sm font-medium truncate">{profile.email}</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <div className="min-w-0 flex-1">
                  <p className="text-xs text-muted-foreground">Phone</p>
                  <p className="text-sm font-medium">{profile.phone || 'Not provided'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Account Info */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Account
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                <UserIcon className="w-4 h-4 text-muted-foreground" />
                <div className="min-w-0 flex-1">
                  <p className="text-xs text-muted-foreground">User ID</p>
                  <p className="text-sm font-mono font-medium truncate">{profile.id}</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <div className="min-w-0 flex-1">
                  <p className="text-xs text-muted-foreground">Last Login</p>
                  <p className="text-sm font-medium">{formatDate(profile.lastLoginAt || profile.createdAt || new Date())}</p>
                </div>
              </div>
              {profile.createdAt && (
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <div className="min-w-0 flex-1">
                    <p className="text-xs text-muted-foreground">Member Since</p>
                    <p className="text-sm font-medium">{formatDate(profile.createdAt)}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Status Footer */}
        <div className="mt-auto pt-4 border-t border-muted/50 flex justify-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <div className="absolute inset-0 w-2 h-2 bg-green-500 rounded-full animate-ping opacity-75" />
              </div>
              <span className="text-xs font-medium text-green-600 dark:text-green-400">Active</span>
            </div>
            <div className="text-xs text-muted-foreground">Updated {new Date().toLocaleDateString()}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfilePage;