"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Plus, Search, Edit, Trash2, Eye, MoreHorizontal, Building } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialog<PERSON>ontent, AlertDialogDescription, <PERSON>ert<PERSON>ialog<PERSON>oot<PERSON>, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"

const mallData = {
  id: 1,
  name: "Grand Central Plaza",
  floors: 4,
}

const shops = [
  {
    id: 1,
    name: "Apple Store",
    category: "Electronics",
    floor: 1,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 9:00 PM",
  },
  {
    id: 2,
    name: "Zara",
    category: "Fashion",
    floor: 1,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 10:00 PM",
  },
  {
    id: 3,
    name: "Starbucks",
    category: "Food & Beverage",
    floor: 1,
    status: "Active",
    phone: "+****************",
    hours: "6:00 AM - 10:00 PM",
  },
  {
    id: 4,
    name: "Nike",
    category: "Sports",
    floor: 1,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 9:00 PM",
  },
  {
    id: 5,
    name: "H&M",
    category: "Fashion",
    floor: 2,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 10:00 PM",
  },
  {
    id: 6,
    name: "Sephora",
    category: "Beauty",
    floor: 2,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 9:00 PM",
  },
  {
    id: 7,
    name: "GameStop",
    category: "Electronics",
    floor: 2,
    status: "Draft",
    phone: "+****************",
    hours: "10:00 AM - 9:00 PM",
  },
  {
    id: 8,
    name: "Bath & Body Works",
    category: "Beauty",
    floor: 2,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 9:00 PM",
  },
  {
    id: 9,
    name: "Food Court",
    category: "Food & Beverage",
    floor: 3,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 10:00 PM",
  },
  {
    id: 10,
    name: "Target",
    category: "Department Store",
    floor: 3,
    status: "Active",
    phone: "+****************",
    hours: "8:00 AM - 10:00 PM",
  },
  {
    id: 11,
    name: "Best Buy",
    category: "Electronics",
    floor: 3,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 9:00 PM",
  },
  {
    id: 12,
    name: "Cinema Complex",
    category: "Entertainment",
    floor: 4,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 12:00 AM",
  },
  {
    id: 13,
    name: "Kids Zone",
    category: "Entertainment",
    floor: 4,
    status: "Active",
    phone: "+****************",
    hours: "10:00 AM - 8:00 PM",
  },
  {
    id: 14,
    name: "Luxury Lounge",
    category: "Services",
    floor: 4,
    status: "Inactive",
    phone: "+****************",
    hours: "10:00 AM - 8:00 PM",
  },
]

export default function MallShopsPage({ params }: { params: { id: string } }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("All")
  const [floorFilter, setFloorFilter] = useState("All")
  const [statusFilter, setStatusFilter] = useState("All")
  const [deleteId, setDeleteId] = useState<number | null>(null)

  const categories = ["All", ...Array.from(new Set(shops.map(shop => shop.category)))]
  const floors = ["All", ...Array.from(new Set(shops.map(shop => shop.floor))).sort()]

  const filteredShops = shops.filter((shop) => {
    const matchesSearch = shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shop.category.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "All" || shop.category === categoryFilter
    const matchesFloor = floorFilter === "All" || shop.floor.toString() === floorFilter
    const matchesStatus = statusFilter === "All" || shop.status === statusFilter
    return matchesSearch && matchesCategory && matchesFloor && matchesStatus
  })

  const shopsByFloor = floors.slice(1).reduce((acc, floor) => {
    acc[floor] = shops.filter(shop => shop.floor.toString() === floor.toString())
    return acc
  }, {} as Record<string, typeof shops>)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
      case "Inactive":
        return <Badge variant="secondary">Inactive</Badge>
      case "Draft":
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleDelete = (id: number) => {
    console.log("Delete shop with id:", id)
    setDeleteId(null)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        {/* <Button variant="ghost" asChild>
          <Link href="/admin/malls">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Malls
          </Link>
        </Button> */}
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">Manage Shops</h1>
          <p className="text-muted-foreground">{mallData.name} - {mallData.floors} floors</p>
        </div>
        <Button asChild>
          <Link href={`/admin/shops/new?mallId=${params.id}`}>
            <Plus className="mr-2 h-4 w-4" />
            Add Shop
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shops</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shops.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shops</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shops.filter(s => s.status === "Active").length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length - 1}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Floors Used</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{floors.length - 1}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search shops..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={floorFilter} onValueChange={setFloorFilter}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Floor" />
              </SelectTrigger>
              <SelectContent>
                {floors.map((floor) => (
                  <SelectItem key={floor} value={floor.toString()}>
                    {floor === "All" ? "All Floors" : `Floor ${floor}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Status</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Shops Content */}
      <Tabs defaultValue="list" className="w-full">
        <TabsList>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="floor">By Floor</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shops ({filteredShops.length})</CardTitle>
              <CardDescription>All shops in this mall</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Floor</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Hours</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredShops.map((shop) => (
                    <TableRow key={shop.id}>
                      <TableCell className="font-medium">{shop.name}</TableCell>
                      <TableCell>{shop.category}</TableCell>
                      <TableCell>Floor {shop.floor}</TableCell>
                      <TableCell>{getStatusBadge(shop.status)}</TableCell>
                      <TableCell>{shop.phone}</TableCell>
                      <TableCell>{shop.hours}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/malls/${params.id}/shops/${shop.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/shops/${shop.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => setDeleteId(shop.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="floor" className="space-y-6">
          {Object.entries(shopsByFloor).map(([floor, floorShops]) => (
            <Card key={floor}>
              <CardHeader>
                <CardTitle>Floor {floor} ({floorShops.length} shops)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {floorShops.map((shop) => (
                    <Card key={shop.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">{shop.name}</CardTitle>
                          {getStatusBadge(shop.status)}
                        </div>
                        <CardDescription>{shop.category}</CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2 text-sm text-muted-foreground">
                          <div>{shop.phone}</div>
                          <div>{shop.hours}</div>
                        </div>
                        <div className="flex gap-2 mt-4">
                          <Button size="sm" variant="outline" asChild className="flex-1">
                            <Link href={`/admin/shops/${shop.id}/edit`}>
                              <Edit className="mr-1 h-3 w-3" />
                              Edit
                            </Link>
                          </Button>
                          <Button size="sm" variant="outline" asChild>
                            <Link href={`/malls/${params.id}/shops/${shop.id}`}>
                              <Eye className="h-3 w-3" />
                            </Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteId !== null} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the shop.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => deleteId && handleDelete(deleteId)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
