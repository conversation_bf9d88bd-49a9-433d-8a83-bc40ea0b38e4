// Application constants

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  SHOPS_PER_PAGE: 12,
  FEATURED_MALLS_LIMIT: 6,
  MAX_LIMIT: 100,
} as const

// Debounce delays
export const DEBOUNCE_DELAYS = {
  SEARCH: 300,
  FILTER: 200,
  RESIZE: 150,
} as const

// API endpoints
export const API_ENDPOINTS = {
  MALLS: '/malls',
  FEATURED_MALLS: '/malls/featured',
  SHOPS: '/shops',
  CATEGORIES: '/categories',
  AMENITIES: '/amenities',
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
  },
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
  },
} as const

// Local storage keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh_token',
  USER: 'user',
  THEME: 'theme',
  PREFERENCES: 'preferences',
} as const

// Animation durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000,
} as const

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const

// Default values
export const DEFAULTS = {
  MALL_IMAGE: '/placeholder.svg',
  SHOP_IMAGE: '/placeholder.svg',
  USER_AVATAR: '/placeholder-user.jpg',
  RATING: 0,
  CATEGORY: 'All',
  FLOOR: 'All',
} as const

// Error messages
export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  NOT_FOUND: 'The requested resource was not found.',
  UNAUTHORIZED: 'You are not authorized to access this resource.',
  VALIDATION: 'Please check your input and try again.',
  SERVER: 'Server error. Please try again later.',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  CREATED: 'Successfully created!',
  UPDATED: 'Successfully updated!',
  DELETED: 'Successfully deleted!',
  SAVED: 'Successfully saved!',
} as const

// Filter options
export const FILTER_OPTIONS = {
  SORT_BY: [
    { value: 'name', label: 'Name' },
    { value: 'rating', label: 'Rating' },
    { value: 'created', label: 'Newest' },
    { value: 'popular', label: 'Most Popular' },
  ],
  VIEW_MODES: [
    { value: 'grid', label: 'Grid', icon: 'Grid' },
    { value: 'list', label: 'List', icon: 'List' },
    { value: 'floor', label: 'Floor', icon: 'Building' },
  ],
} as const

// Status types
export const STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  MANAGER: 'manager',
} as const

// File upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MAX_FILES: 10,
} as const
