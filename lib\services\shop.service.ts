import { ApiError } from "../api-error";
import { Prisma, Shop } from "../generated/prisma";
import prisma from "../prisma";
import { ApiResponse } from "../types/api-response";
import { ShopCreateInput, ShopDetailsPageData, ShopUpdateInput } from "../types/shop";

export const getShops = async (options: {
  page?: number;
  limit?: number;
  mallId?: string | null;
  floor?: string | null;
  category?: string | null;
}): Promise<ApiResponse<Shop[]>> => {
  try {
    const { page = 1, limit = 10, mallId, floor, category } = options;
    const skip = (page - 1) * limit;

    const where: any = {
      ...(mallId && { mallId }),
      ...(floor && { floor: Number(floor) }),
      ...(category && category.toLowerCase() !== 'all' && {
        category: { contains: category, mode: 'insensitive' as Prisma.QueryMode },
      }),
    };

    const [shops, total] = await Promise.all([
      prisma.shop.findMany({
        where,
        skip,
        take: limit,
      }),
      prisma.shop.count({ where }),
    ]);

    return {
      success: true,
      data: shops,
      meta: {
        page,
        limit,
        total,
        hasNext: total > page * limit,
      },
    };
  } catch (err: any) {
    throw new ApiError(400, err.message);
  }
};

export const getShopsByFloorId = async (floorId: string): Promise<ApiResponse<Shop[]>> => {
  try {
    const floor = await prisma.floor.findUnique({
      where: { id: floorId },
      select: { categories: true, number: true },
    });

    if (!floor) {
      throw new ApiError(404, 'Floor not found');
    }

    const shops = await prisma.shop.findMany({
      where: {
        floor: floor.number,
        ...(floor.categories.length > 0 && {
          category: { in: floor.categories, mode: 'insensitive' as Prisma.QueryMode },
        }),
      },
    });

    return {
      success: true,
      data: shops,
    };
  } catch (err: any) {
    throw new ApiError(400, err.message);
  }
};

export const getShopsByMallId = async (mallId: string): Promise<ApiResponse<Shop[]>> => {
  try {
    const floors = await prisma.floor.findMany({
      where: { mallId },
      select: { categories: true, number: true },
    });

    const categories = floors.flatMap((floor) => floor.categories);
    const floorNumbers = floors.map((floor) => floor.number);

    const shops = await prisma.shop.findMany({
      where: {
        mallId,
        ...(categories.length > 0 && {
          category: { in: categories, mode: 'insensitive' as Prisma.QueryMode },
        }),
        ...(floorNumbers.length > 0 && {
          floor: { in: floorNumbers },
        }),
      },
    });

    return {
      success: true,
      data: shops,
    };
  } catch (err: any) {
    throw new ApiError(400, err.message);
  }
};

export const getShopById = async (id: string): Promise<ApiResponse<ShopDetailsPageData>> => {
  try {
    const shop = await prisma.shop.findUnique({
      where: { id },
      include: { hours: true },
    });

    if (!shop) {
      throw new ApiError(404, 'Shop not found');
    }

    const shopDetails: ShopDetailsPageData = {
      ...shop,
      hours: shop.hours.filter((hour) => !hour.isClosed),
      category: shop.category,
    };

    return {
      success: true,
      data: shopDetails,
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const createShop = async (input: ShopCreateInput): Promise<ApiResponse<Shop>> => {
  try {
    const shop = await prisma.shop.create({ data: input });
    return {
      success: true,
      data: shop,
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const updateShop = async (id: string, input: ShopUpdateInput): Promise<ApiResponse<Shop>> => {
  try {
    const shop = await prisma.shop.update({ where: { id }, data: input });
    if (!shop) {
      throw new ApiError(404, 'Shop not found');
    }
    return {
      success: true,
      data: shop,
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const deleteShop = async (id: string): Promise<ApiResponse<null>> => {
  try {
    const shop = await prisma.shop.delete({ where: { id } });
    if (!shop) {
      throw new ApiError(404, 'Shop not found');
    }
    return {
      success: true,
      data: null,
      message: 'Shop deleted successfully',
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};