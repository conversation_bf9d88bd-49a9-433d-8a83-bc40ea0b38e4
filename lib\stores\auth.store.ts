import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface AuthState {
    accessToken: string | null;
    setAccessToken: (token: string | null) => void;
    isAuthenticated: boolean;
    setAuthenticated: (val: boolean) => void;
    logout: () => void;
}

const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            accessToken: null,
            setAccessToken: (token) => set({ accessToken: token }),
            isAuthenticated: false,
            setAuthenticated: (val) => set({ isAuthenticated: val }),
            logout: () => set({ accessToken: null, isAuthenticated: false }),
        }),
        {
            name: 'auth-store',
            storage: createJSONStorage(() => sessionStorage),
            partialize: (state) => ({
                accessToken: state.accessToken,
                isAuthenticated: state.isAuthenticated,
            }),
        }
    )
)

export default useAuthStore
