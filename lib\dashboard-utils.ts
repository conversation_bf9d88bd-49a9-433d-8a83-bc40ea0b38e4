import { Building2, ShoppingBag, Users, TrendingUp, ArrowUpRight, ArrowDownRight } from 'lucide-react';

export interface DashboardCard {
  title: string;
  value: number;
  difference: number | string;
  icon: any; // Lucide icon component
  trend: 'up' | 'down';
}

export class DashboardUtils {
  static generateDashboardCards(stats: {
    totalMalls: number;
    mallsDifference: number;
    totalShops: number;
    shopsDifference: number;
    totalUsers: number;
    usersDifference: number;
    totalActiveUsers: number;
    activeUsersDifferencePercentage: number;
    totalPageViews: number;
    pageViewsDifferencePercentage: number;
  }): DashboardCard[] {
    return [
      {
        title: 'Total Malls',
        value: stats.totalMalls,
        difference: stats.mallsDifference >= 0 ? `+${stats.mallsDifference}` : stats.mallsDifference,
        icon: Building2,
        trend: stats.mallsDifference >= 0 ? 'up' : 'down',
      },
      {
        title: 'Total Shops',
        value: stats.totalShops,
        difference: stats.shopsDifference >= 0 ? `+${stats.shopsDifference}` : stats.shopsDifference,
        icon: ShoppingBag,
        trend: stats.shopsDifference >= 0 ? 'up' : 'down',
      },
      {
        title: 'Active Users',
        value: stats.totalActiveUsers,
        difference: `${stats.activeUsersDifferencePercentage}%`,
        icon: Users,
        trend: stats.activeUsersDifferencePercentage >= 0 ? 'up' : 'down',
      },
      {
        title: 'Page Views',
        value: stats.totalPageViews,
        difference: `${stats.pageViewsDifferencePercentage}%`,
        icon: TrendingUp,
        trend: stats.pageViewsDifferencePercentage >= 0 ? 'up' : 'down',
      },
    ];
  }
}