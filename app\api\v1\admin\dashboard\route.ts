
import { NextRequest, NextResponse } from 'next/server';
import { errorHandler } from '@/lib/api-error';
import { getTotalMallsAndDifference, getTotalShopsAndDifference, getTotalUsersAndDifference, getTotalActiveUsersAndPercentage, getTotalPageViewsAndPercentage, getRecentMalls, getRecentReports, getRecentShops, getRecentUsers } from '@/lib/services/dashboard.service';
import { authMiddleware } from '@/lib/middleware/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authMiddleware(request);
    if (user.role !== 'admin') {
      throw new Error('Unauthorized');
    }

    // Fetch all data in parallel
    const [malls, shops, users, activeUsers, pageViews, recentMalls, recentShops, recentReports, recentUsers] = await Promise.all([
      getTotalMallsAndDifference(),
      getTotalShopsAndDifference(),
      getTotalUsersAndDifference(),
      getTotalActiveUsersAndPercentage(),
      getTotalPageViewsAndPercentage(),
      getRecentMalls(),
      getRecentShops(),
      getRecentReports(),
      getRecentUsers()
    ]);

    const stats = {
      totalMalls: malls.total,
      mallsDifference: malls.difference,
      totalShops: shops.total,
      shopsDifference: shops.difference,
      totalUsers: users.total,
      usersDifference: users.difference,
      totalActiveUsers: activeUsers.total,
      activeUsersDifferencePercentage: activeUsers.percentageDifference,
      totalPageViews: pageViews.total,
      pageViewsDifferencePercentage: pageViews.percentageDifference,
      recentMalls: recentMalls,
      recentShops: recentShops,
      recentReports: recentReports,
      recentUsers: recentUsers
    };

    // const cards = DashboardUtils.generateDashboardCards(stats);

    return NextResponse.json({ success: true, data: { ...stats } });
  }
  catch (error) {
    return errorHandler(error);
  }
}

