"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Upload, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

const malls = [
  { id: 1, name: "Grand Central Plaza", floors: 4 },
  { id: 2, name: "Riverside Mall", floors: 3 },
  { id: 3, name: "Tech Hub Center", floors: 5 },
  { id: 4, name: "Fashion District", floors: 3 },
  { id: 5, name: "Family Fun Center", floors: 2 },
  { id: 6, name: "Outlet Paradise", floors: 2 },
]

const categories = [
  "Fashion",
  "Electronics",
  "Food & Beverage",
  "Beauty",
  "Sports",
  "Entertainment",
  "Department Store",
  "Services",
  "Books & Media",
  "Home & Garden",
  "Jewelry",
  "Toys & Games",
]

const servicesList = [
  "Technical Support",
  "Device Setup",
  "Data Transfer",
  "Repairs",
  "Workshops",
  "Personal Shopping",
  "Gift Wrapping",
  "Delivery",
  "Installation",
  "Consultation",
]

// Mock data for editing
const mockShopData = {
  1: {
    id: 1,
    name: "Apple Store",
    category: "Electronics",
    mallId: 1,
    floor: 1,
    description: "Latest Apple products and accessories",
    fullDescription:
      "The Apple Store at Grand Central Plaza offers the complete Apple experience with hands-on demonstrations of the latest products.",
    phone: "+****************",
    email: "<EMAIL>",
    website: "www.apple.com/retail/grandcentral",
    hours: "10:00 AM - 9:00 PM",
    status: "Active",
    services: ["Technical Support", "Device Setup", "Data Transfer", "Repairs", "Workshops"],
    products: ["iPhone", "iPad", "Mac", "Apple Watch", "AirPods"],
    features: ["Genius Bar Support", "Personal Setup", "Today at Apple Sessions"],
    images: [
      "/placeholder.svg?height=300&width=400&text=Store",
      "/placeholder.svg?height=300&width=400&text=Interior",
      "/placeholder.svg?height=300&width=400&text=Products",
    ],
  },
}

export default function EditShopPage() {
  const router = useRouter()
  const params = useParams() as { id: string }
  const shopId = Number.parseInt(params.id)

  const [formData, setFormData] = useState({
    name: "",
    category: "",
    mallId: "",
    floor: 1,
    description: "",
    fullDescription: "",
    phone: "",
    email: "",
    website: "",
    hours: "",
    status: "Draft",
    services: [] as string[],
    products: [] as string[],
    features: [] as string[],
  })
  const [images, setImages] = useState<string[]>([])
  const [selectedMall, setSelectedMall] = useState<(typeof malls)[0] | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load shop data
    const shopData = mockShopData[shopId as keyof typeof mockShopData]
    if (shopData) {
      setFormData({
        name: shopData.name,
        category: shopData.category,
        mallId: shopData.mallId.toString(),
        floor: shopData.floor,
        description: shopData.description,
        fullDescription: shopData.fullDescription,
        phone: shopData.phone,
        email: shopData.email,
        website: shopData.website,
        hours: shopData.hours,
        status: shopData.status,
        services: shopData.services,
        products: shopData.products,
        features: shopData.features,
      })
      setImages(shopData.images)

      const mall = malls.find((m) => m.id === shopData.mallId)
      setSelectedMall(mall || null)
    }
    setLoading(false)
  }, [shopId])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Updated form data:", formData)
    console.log("Updated images:", images)
    router.push("/admin/shops")
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        services: [...prev.services, service],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        services: prev.services.filter((s) => s !== service),
      }))
    }
  }

  const addProduct = () => {
    const product = prompt("Enter product name:")
    if (product && product.trim()) {
      setFormData((prev) => ({
        ...prev,
        products: [...prev.products, product.trim()],
      }))
    }
  }

  const removeProduct = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      products: prev.products.filter((_, i) => i !== index),
    }))
  }

  const addFeature = () => {
    const feature = prompt("Enter feature name:")
    if (feature && feature.trim()) {
      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, feature.trim()],
      }))
    }
  }

  const removeFeature = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }))
  }

  const addImage = () => {
    const newImage = `/placeholder.svg?height=300&width=400&text=New${images.length + 1}`
    setImages([...images, newImage])
  }

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index))
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        {/* <Button variant="ghost" asChild>
          <Link href="/admin/shops">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Shops
          </Link>
        </Button> */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Shop</h1>
          <p className="text-muted-foreground">Update shop information</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Update the basic details of the shop</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Shop Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter shop name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="mall">Mall *</Label>
                    <Select
                      value={formData.mallId}
                      onValueChange={(value) => {
                        setFormData((prev) => ({ ...prev, mallId: value }))
                        const mall = malls.find((m) => m.id.toString() === value)
                        setSelectedMall(mall || null)
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select mall" />
                      </SelectTrigger>
                      <SelectContent>
                        {malls.map((mall) => (
                          <SelectItem key={mall.id} value={mall.id.toString()}>
                            {mall.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="floor">Floor *</Label>
                    <Select
                      value={formData.floor.toString()}
                      onValueChange={(value) => setFormData((prev) => ({ ...prev, floor: Number.parseInt(value) }))}
                      disabled={!selectedMall}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select floor" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedMall &&
                          Array.from({ length: selectedMall.floors }, (_, i) => i + 1).map((floor) => (
                            <SelectItem key={floor} value={floor.toString()}>
                              Floor {floor}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Short Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of the shop"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fullDescription">Full Description</Label>
                  <Textarea
                    id="fullDescription"
                    value={formData.fullDescription}
                    onChange={(e) => setFormData((prev) => ({ ...prev, fullDescription: e.target.value }))}
                    placeholder="Detailed description of the shop and its offerings"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Draft">Draft</SelectItem>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>How customers can reach the shop</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                      placeholder="+****************"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => setFormData((prev) => ({ ...prev, website: e.target.value }))}
                      placeholder="https://www.shop.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="hours">Operating Hours</Label>
                    <Input
                      id="hours"
                      value={formData.hours}
                      onChange={(e) => setFormData((prev) => ({ ...prev, hours: e.target.value }))}
                      placeholder="10:00 AM - 9:00 PM"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Products & Features</CardTitle>
                <CardDescription>What the shop offers</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Label>Products</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addProduct}>
                      Add Product
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.products.map((product, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {product}
                        <X className="h-3 w-3 cursor-pointer" onClick={() => removeProduct(index)} />
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Label>Features</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addFeature}>
                      Add Feature
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.features.map((feature, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {feature}
                        <X className="h-3 w-3 cursor-pointer" onClick={() => removeFeature(index)} />
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Images</CardTitle>
                <CardDescription>Update images of the shop</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image || "/placeholder.svg"}
                        alt={`Shop image ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => removeImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  <Button type="button" variant="outline" className="h-24 border-dashed" onClick={addImage}>
                    <Upload className="h-4 w-4 mr-2" />
                    Add Image
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Services</CardTitle>
                <CardDescription>Select services offered</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {servicesList.map((service) => (
                    <div key={service} className="flex items-center space-x-2">
                      <Checkbox
                        id={`service-${service}`}
                        checked={formData.services.includes(service)}
                        onCheckedChange={(checked) => handleServiceChange(service, checked as boolean)}
                      />
                      <Label htmlFor={`service-${service}`} className="text-sm">
                        {service}
                      </Label>
                    </div>
                  ))}
                </div>
                {formData.services.length > 0 && (
                  <div className="mt-4 pt-4 border-t">
                    <p className="text-sm font-medium mb-2">Selected ({formData.services.length}):</p>
                    <div className="flex flex-wrap gap-1">
                      {formData.services.slice(0, 3).map((service) => (
                        <Badge key={service} variant="outline" className="text-xs">
                          {service}
                        </Badge>
                      ))}
                      {formData.services.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{formData.services.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {selectedMall && (
              <Card>
                <CardHeader>
                  <CardTitle>Mall Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">Mall:</span> {selectedMall.name}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Total Floors:</span> {selectedMall.floors}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Selected Floor:</span> {formData.floor}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button type="button" variant="outline" asChild>
            <Link href="/admin/shops">Cancel</Link>
          </Button>
          <Button type="submit">Update Shop</Button>
        </div>
      </form>
    </div>
  )
}
