import { ApiError } from '../api-error';
import { Mall, Prisma, Shop } from '../generated/prisma';
import { MallCreateInputSchema } from '../generated/zod';
import prisma from '../prisma';
import { ApiResponse } from '../types/api-response';
import { FeaturedMallData, GetMallsOptions, MallCreateInput, MallDetailsPageData, MallUpdateInput, ShopCardData } from '../types/mall';

export const getMalls = async (options: GetMallsOptions): Promise<ApiResponse<Mall[]>> => {
  try {
    const { page = 1, limit = 10, activeOnly, searchTerm, category, city, state, country, amenities, sortBy } = options;
    const skip = (page - 1) * limit;

    const where = {
      ...(activeOnly && { isActive: true }),
      ...(searchTerm && {
        OR: [
          { name: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { shortDescription: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { description: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { slug: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { pinCode: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { landmark: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { address: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { city: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { state: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          { country: { contains: searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
        ],
      }),
      ...(category && category.toLowerCase() !== 'all' && {
        floors: {
          some: {
            categories: {
              has: category.toLowerCase(),
            },
          },
        },
      }),
      ...(city && city.toLowerCase() !== 'all' && {
        city: { contains: city, mode: 'insensitive' as Prisma.QueryMode },
      }),
      ...(state && state.toLowerCase() !== 'all' && {
        state: { contains: state, mode: 'insensitive' as Prisma.QueryMode },
      }),
      ...(country && country.toLowerCase() !== 'all' && {
        country: { contains: country, mode: 'insensitive' as Prisma.QueryMode },
      }),
      ...(amenities && amenities.length > 0 && {
        amenities: {
          hasSome: amenities,
        },
      }),
    };

    const [malls, total] = await Promise.all([
      prisma.mall.findMany({
        where,
        skip,
        take: limit,
        include: {
          floors: {
            select: {
              categories: true,
            },
          },
        },
        orderBy: sortBy ? { [sortBy]: 'asc' } : undefined,
      }),
      prisma.mall.count({ where }),
    ]);

    return {
      success: true,
      data: malls,
      meta: {
        page,
        limit,
        total,
        hasNext: total > page * limit,
      },
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const getMallById = async (id: string): Promise<ApiResponse<Mall>> => {
  try {
    const mall = await prisma.mall.findUnique({ where: { id } });
    if (!mall) {
      throw new ApiError(404, 'Mall not found');
    }

    return {
      success: true,
      data: mall,
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const getMallBySlug = async (slug: string): Promise<ApiResponse<MallDetailsPageData>> => {
  try {
    const mall = await prisma.mall.findUnique({
      where: { slug },
      include: {
        mallHours: true,
        floors: {
          select: {
            id: true,
            number: true,
            name: true,
            categories: true,
          },
        },
      },
    });
    if (!mall) {
      throw new ApiError(404, 'Mall not found');
    }

    const mallDetails: MallDetailsPageData = {
      ...mall,
      floors: mall.floors.map((floor) => ({
        id: floor.id,
        number: floor.number,
        name: floor.name,
      })),
      categories: mall.floors.flatMap((floor) => floor.categories),
      mallHours: mall.mallHours,
      amenities: mall.amenities,
    };

    return {
      success: true,
      data: mallDetails,
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const createMall = async (input: MallCreateInput): Promise<ApiResponse<Mall>> => {
  try {
    const parsedInput = MallCreateInputSchema.parse(input);
    const mall = await prisma.mall.create({ data: { ...parsedInput } });
    return {
      success: true,
      data: mall,
    };
  } catch (error: any) {
    if (error.code === 'P2002') {
      throw new ApiError(400, 'Mall with this slug already exists');
    }
    throw new ApiError(400, error.message);
  }
};

export const updateMall = async (id: string, input: MallUpdateInput): Promise<ApiResponse<Mall>> => {
  try {
    const mall = await prisma.mall.update({ where: { id }, data: input });
    if (!mall) {
      throw new ApiError(404, 'Mall not found');
    }
    return {
      success: true,
      data: mall,
    };
  } catch (error: any) {
    if (error.code === 'P2002') {
      throw new ApiError(400, 'Mall with this slug already exists');
    }
    throw new ApiError(400, error.message);
  }
};

export const deleteMall = async (id: string): Promise<ApiResponse<null>> => {
  try {
    const mall = await prisma.mall.delete({ where: { id } });
    if (!mall) {
      throw new ApiError(404, 'Mall not found');
    }
    return {
      success: true,
      data: null,
      message: 'Mall deleted successfully',
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};

export const getFeaturedMalls = async ({
  isActiveOnly = true,
  page = 1,
  limit = 10,
}: {
  isActiveOnly?: boolean;
  page?: number;
  limit?: number;
}): Promise<ApiResponse<FeaturedMallData[]>> => {
  try {
    const skip = (page - 1) * limit;

    const malls = await prisma.mall.findMany({
      where: {
        featured: true,
        ...(isActiveOnly && { isActive: true }),
      },
      select: {
        id: true,
        name: true,
        slug: true,
        logo: true,
        rating: true,
        city: true,
        state: true,
        address: true,
        country: true,
        shortDescription: true,
        images: true,
        averageFootFall: true,
        totalShops: true,
        isActive: true,
        amenities: true,
        floors: {
          select: {
            categories: true,
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        rating: 'desc',
      },
    });

    const featuredMalls: FeaturedMallData[] = malls.map((mall) => ({
      id: mall.id,
      name: mall.name,
      slug: mall.slug,
      logo: mall.logo,
      rating: mall.rating,
      city: mall.city,
      state: mall.state,
      address: mall.address,
      country: mall.country,
      shortDescription: mall.shortDescription,
      image: mall.images[0],
      averageFootFall: mall.averageFootFall,
      totalShops: mall.totalShops,
      totalFloors: mall.floors.length,
      categories: mall.floors.flatMap((floor) => floor.categories),
      amenities: mall.amenities,
      isActive: mall.isActive,
    }));

    const total = await prisma.mall.count({
      where: {
        featured: true,
        ...(isActiveOnly && { isActive: true }),
      },
    });

    return {
      success: true,
      data: featuredMalls,
      meta: {
        page,
        limit,
        total,
        hasNext: total > page * limit,
      },
    };
  } catch (err: any) {
    throw new ApiError(400, err.message);
  }
};

export const getMallShops = async (
  mallId: string,
  options?: {
    page?: number;
    limit?: number;
    searchTerm?: string;
    category?: string;
    floor?: string;
  }
): Promise<ApiResponse<ShopCardData[]>> => {
  try {
    const shops = await prisma.shop.findMany({
      where: {
        mallId,
        ...(options?.searchTerm && options?.searchTerm.toLowerCase() !== '' && {
          OR: [
            { name: { contains: options.searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
            { category: { contains: options.searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          ],
        }),
        ...(options?.category && options?.category.toLowerCase() !== 'all' && {
          category: { contains: options.category, mode: 'insensitive' as Prisma.QueryMode },
        }),
        ...(options?.floor && options?.floor?.toLowerCase() !== 'all' && {
          floor: Number(options.floor),
        }),
      },
      include: {
        hours: true,
      },
      skip: options?.page ? (options.page - 1) * options.limit! : undefined,
      take: options?.limit,
    });

    const total = await prisma.shop.count({
      where: {
        mallId,
        ...(options?.searchTerm && options?.searchTerm.toLowerCase() !== '' && {
          OR: [
            { name: { contains: options.searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
            { category: { contains: options.searchTerm, mode: 'insensitive' as Prisma.QueryMode } },
          ],
        }),
        ...(options?.category && options?.category.toLowerCase() !== 'all' && {
          category: { contains: options.category, mode: 'insensitive' as Prisma.QueryMode },
        }),
        ...(options?.floor && options?.floor?.toLowerCase() !== 'all' && {
          floor: Number(options.floor),
        }),
      },
    });

    const shopsData: ShopCardData[] = shops.map((shop) => ({
      ...shop,
      hours: shop.hours.filter((hour) => !hour.isClosed),
      category: shop.category,
    }));

    return {
      success: true,
      data: shopsData,
      meta: {
        page: options?.page ?? 1,
        limit: options?.limit ?? 10,
        total: total,
        hasNext: total > (options?.page ?? 1) * (options?.limit! ?? 12),
      },
    };
  } catch (err: any) {
    throw new ApiError(400, err.message);
  }
};