"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Plus, Trash2, Upload, MapPin, Phone, Mail, Car, Users, Building, Star, Calendar, User, Shield } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"

interface MallCreateInput {
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  logo: string;
  rating: number;
  featured: boolean;
  address: string;
  city: string;
  state: string;
  country: string;
  pinCode: string;
  lat: number;
  lng: number;
  landmark: string;
  phone: string;
  email: string;
  customerCare: string;
  emergency: string;
  website: string;
  openingHours: string;
  parkingAvailable: boolean;
  parkingCapacity: number;
  parkingType: string;
  parkingRates: string;
  averageFootFall: number;
  yearOpened: number;
  company: string;
  manager: string;
  contactPerson: string;
  isActive: boolean;
  createdAt: string;
}

interface FloorDetail {
  id: number;
  name: string;
  description: string;
}

export default function MallCreatePage() {
  const [formData, setFormData] = useState<MallCreateInput>({
    name: "",
    slug: "",
    description: "",
    shortDescription: "",
    logo: "/placeholder.svg?height=100&width=100",
    rating: 0,
    featured: false,
    address: "",
    city: "",
    state: "",
    country: "",
    pinCode: "",
    lat: 0,
    lng: 0,
    landmark: "",
    phone: "",
    email: "",
    customerCare: "",
    emergency: "",
    website: "",
    openingHours: "10:00 AM - 10:00 PM",
    parkingAvailable: false,
    parkingCapacity: 0,
    parkingType: "free",
    parkingRates: "$0/hour",
    averageFootFall: 0,
    yearOpened: new Date().getFullYear(),
    company: "",
    manager: "",
    contactPerson: "",
    isActive: true,
    createdAt: new Date().toISOString(),
  });

  const [amenities, setAmenities] = useState<string[]>([])
  const [newAmenity, setNewAmenity] = useState("")
  const [categories, setCategories] = useState<string[]>([])
  const [newCategory, setNewCategory] = useState("")
  const [floors, setFloors] = useState(1)
  const [floorDetails, setFloorDetails] = useState<FloorDetail[]>([
    { id: 1, name: "Ground Floor", description: "" }
  ])
  const [featuredImage, setFeaturedImage] = useState<string>("/placeholder.svg?height=400&width=800")
  const [galleryImages, setGalleryImages] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: keyof MallCreateInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleAddAmenity = () => {
    if (newAmenity.trim() && !amenities.includes(newAmenity.trim())) {
      setAmenities([...amenities, newAmenity.trim()])
      setNewAmenity("")
    }
  }

  const handleRemoveAmenity = (amenity: string) => {
    setAmenities(amenities.filter((a) => a !== amenity))
  }

  const handleAddCategory = () => {
    if (newCategory.trim() && !categories.includes(newCategory.trim())) {
      setCategories([...categories, newCategory.trim()])
      setNewCategory("")
    }
  }

  const handleRemoveCategory = (category: string) => {
    setCategories(categories.filter((c) => c !== category))
  }

  const updateFloorCount = (count: number) => {
    const newCount = Math.max(1, count)
    setFloors(newCount)

    if (newCount > floorDetails.length) {
      const newFloors = Array.from({ length: newCount - floorDetails.length }).map((_, i) => ({
        id: floorDetails.length + i + 1,
        name: `Floor ${floorDetails.length + i + 1}`,
        description: "",
      }))
      setFloorDetails([...floorDetails, ...newFloors])
    } else if (newCount < floorDetails.length) {
      setFloorDetails(floorDetails.slice(0, newCount))
    }
  }

  const updateFloorDetail = (index: number, field: "name" | "description", value: string) => {
    const updatedFloors = [...floorDetails]
    updatedFloors[index] = { ...updatedFloors[index], [field]: value }
    setFloorDetails(updatedFloors)
  }

  const handleRemoveGalleryImage = (index: number) => {
    setGalleryImages(galleryImages.filter((_, i) => i !== index))
  }

  const handleAddGalleryImage = () => {
    const newImage = "/placeholder.svg?height=300&width=400"
    setGalleryImages([...galleryImages, newImage])
  }

  const handleSubmit = async (isDraft: boolean = false) => {
    try {
      setIsSubmitting(true);

      const mallData = {
        ...formData,
        amenities,
        categories,
        floorDetails,
        featuredImage,
        galleryImages,
        status: isDraft ? 'draft' : 'active'
      };

      // Replace with your actual API call
      console.log('Mall data to submit:', mallData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      alert(`Mall ${isDraft ? 'saved as draft' : 'created'} successfully!`);
    } catch (error) {
      console.error('Error creating mall:', error);
      alert('Error creating mall. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="ghost" asChild className="mb-2">
            <Link href="/admin/malls">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Malls
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">Create New Mall</h2>
          <p className="text-muted-foreground">Add a new shopping mall to your platform.</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => handleSubmit(true)}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save as Draft"}
          </Button>
          <Button
            onClick={() => handleSubmit(false)}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Creating..." : "Create Mall"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="details" className="space-y-6">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="floors">Floors</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="w-5 h-5" />
                Basic Information
              </CardTitle>
              <CardDescription>Enter the basic details about the mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Mall Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => {
                    handleInputChange('name', e.target.value);
                    handleInputChange('slug', generateSlug(e.target.value));
                  }}
                  placeholder="Enter mall name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  placeholder="mall-url-slug"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="shortDescription">Short Description *</Label>
                <Input
                  id="shortDescription"
                  value={formData.shortDescription}
                  onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                  placeholder="Brief description for listings"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Detailed Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Detailed description of the mall"
                  className="min-h-[120px]"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="www.mallname.com"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="openingHours">Opening Hours</Label>
                  <Input
                    id="openingHours"
                    value={formData.openingHours}
                    onChange={(e) => handleInputChange('openingHours', e.target.value)}
                    placeholder="10:00 AM - 10:00 PM"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Location Details
              </CardTitle>
              <CardDescription>Specify the mall's location and address.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="address">Full Address *</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Street address"
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="City name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="state">State *</Label>
                  <Input
                    id="state"
                    value={formData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    placeholder="State/Province"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="country">Country *</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    placeholder="Country"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pinCode">PIN Code *</Label>
                  <Input
                    id="pinCode"
                    value={formData.pinCode}
                    onChange={(e) => handleInputChange('pinCode', e.target.value)}
                    placeholder="Postal/ZIP code"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="lat">Latitude</Label>
                  <Input
                    id="lat"
                    type="number"
                    step="0.000001"
                    value={formData.lat || ''}
                    onChange={(e) => handleInputChange('lat', parseFloat(e.target.value))}
                    placeholder="12.9716"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lng">Longitude</Label>
                  <Input
                    id="lng"
                    type="number"
                    step="0.000001"
                    value={formData.lng || ''}
                    onChange={(e) => handleInputChange('lng', parseFloat(e.target.value))}
                    placeholder="77.5946"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="landmark">Landmark</Label>
                <Input
                  id="landmark"
                  value={formData.landmark}
                  onChange={(e) => handleInputChange('landmark', e.target.value)}
                  placeholder="Nearby landmark"
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="w-5 h-5" />
                Contact Information
              </CardTitle>
              <CardDescription>Add contact details for the mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="****** 567 890"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customerCare">Customer Care</Label>
                  <Input
                    id="customerCare"
                    value={formData.customerCare}
                    onChange={(e) => handleInputChange('customerCare', e.target.value)}
                    placeholder="****** 567 891"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="emergency">Emergency Contact</Label>
                  <Input
                    id="emergency"
                    value={formData.emergency}
                    onChange={(e) => handleInputChange('emergency', e.target.value)}
                    placeholder="****** 567 999"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Categories and Amenities */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
                <CardDescription>Add categories for this mall.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a category"
                    value={newCategory}
                    onChange={(e) => setNewCategory(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleAddCategory()}
                  />
                  <Button type="button" size="icon" onClick={handleAddCategory}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {categories.map((category) => (
                    <Badge key={category} variant="secondary" className="flex items-center gap-1">
                      {category}
                      <button
                        onClick={() => handleRemoveCategory(category)}
                        className="ml-1 text-muted-foreground hover:text-foreground"
                      >
                        <Trash2 className="h-3 w-3" />
                        <span className="sr-only">Remove {category}</span>
                      </button>
                    </Badge>
                  ))}
                  {categories.length === 0 && <p className="text-sm text-muted-foreground">No categories added yet.</p>}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Amenities</CardTitle>
                <CardDescription>Add amenities available at this mall.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add an amenity"
                    value={newAmenity}
                    onChange={(e) => setNewAmenity(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleAddAmenity()}
                  />
                  <Button type="button" size="icon" onClick={handleAddAmenity}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {amenities.map((amenity) => (
                    <Badge key={amenity} variant="secondary" className="flex items-center gap-1">
                      {amenity}
                      <button
                        onClick={() => handleRemoveAmenity(amenity)}
                        className="ml-1 text-muted-foreground hover:text-foreground"
                      >
                        <Trash2 className="h-3 w-3" />
                        <span className="sr-only">Remove {amenity}</span>
                      </button>
                    </Badge>
                  ))}
                  {amenities.length === 0 && <p className="text-sm text-muted-foreground">No amenities added yet.</p>}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Parking and Management */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Car className="w-5 h-5" />
                  Parking Details
                </CardTitle>
                <CardDescription>Configure parking information.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="parkingAvailable"
                    checked={formData.parkingAvailable}
                    onCheckedChange={(checked) => handleInputChange('parkingAvailable', checked)}
                  />
                  <Label htmlFor="parkingAvailable" className="cursor-pointer">
                    Parking Available
                  </Label>
                </div>
                {formData.parkingAvailable && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="parkingCapacity">Parking Capacity</Label>
                      <Input
                        id="parkingCapacity"
                        type="number"
                        value={formData.parkingCapacity || ''}
                        onChange={(e) => handleInputChange('parkingCapacity', parseInt(e.target.value))}
                        placeholder="300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="parkingType">Parking Type</Label>
                      <Select
                        value={formData.parkingType}
                        onValueChange={(value) => handleInputChange("parkingType", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select parking type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="free">Free</SelectItem>
                          <SelectItem value="paid">Paid</SelectItem>
                          <SelectItem value="valet">Valet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="parkingRates">Parking Rates</Label>
                      <Input
                        id="parkingRates"
                        value={formData.parkingRates}
                        onChange={(e) => handleInputChange('parkingRates', e.target.value)}
                        placeholder="$2/hour"
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Management Details
                </CardTitle>
                <CardDescription>Add management information.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="company">Company/Owner *</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder="Company name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manager">Manager</Label>
                  <Input
                    id="manager"
                    value={formData.manager}
                    onChange={(e) => handleInputChange('manager', e.target.value)}
                    placeholder="Manager name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPerson">Contact Person</Label>
                  <Input
                    id="contactPerson"
                    value={formData.contactPerson}
                    onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                    placeholder="Primary contact person"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="yearOpened">Year Opened</Label>
                    <Input
                      id="yearOpened"
                      type="number"
                      min="1900"
                      max={new Date().getFullYear()}
                      value={formData.yearOpened || ''}
                      onChange={(e) => handleInputChange('yearOpened', parseInt(e.target.value))}
                      placeholder="2012"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="averageFootFall">Daily Footfall</Label>
                    <Input
                      id="averageFootFall"
                      type="number"
                      value={formData.averageFootFall || ''}
                      onChange={(e) => handleInputChange('averageFootFall', parseInt(e.target.value))}
                      placeholder="5000"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="floors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Floor Configuration</CardTitle>
              <CardDescription>Configure the floors and their details.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="floorCount">Number of Floors</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="floorCount"
                    type="number"
                    min={1}
                    value={floors}
                    onChange={(e) => updateFloorCount(Number.parseInt(e.target.value) || 1)}
                    className="w-24"
                  />
                  <span className="text-sm text-muted-foreground">floors</span>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-6">
                {floorDetails.map((floor, index) => (
                  <div key={floor.id} className="space-y-4 p-4 border rounded-md">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Floor {index + 1}</h3>
                      <Badge variant="outline">0 shops</Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`floor-${index}-name`}>Floor Name</Label>
                        <Input
                          id={`floor-${index}-name`}
                          value={floor.name}
                          onChange={(e) => updateFloorDetail(index, "name", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`floor-${index}-description`}>Description</Label>
                        <Input
                          id={`floor-${index}-description`}
                          value={floor.description}
                          onChange={(e) => updateFloorDetail(index, "description", e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" disabled>
                        Add Shops (Available after creation)
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="media" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Featured Image</CardTitle>
              <CardDescription>The main image for this mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="border rounded-md p-2">
                  <Image
                    src={featuredImage}
                    alt="Featured image preview"
                    width={800}
                    height={400}
                    className="w-full h-auto object-cover rounded"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm">
                    Upload Image
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setFeaturedImage("/placeholder.svg?height=400&width=800")}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Gallery Images</CardTitle>
              <CardDescription>Additional images for this mall.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {galleryImages.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {galleryImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <Image
                        src={image}
                        alt={`Gallery image ${index + 1}`}
                        width={400}
                        height={300}
                        className="w-full h-auto object-cover rounded-md"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-md">
                        <Button
                          variant="destructive"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleRemoveGalleryImage(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <div className="border-2 border-dashed rounde
              d-md p-6 flex flex-col items-center justify-center mt-4">
                <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground mb-1">Drag and drop images here or click to browse</p>
                <p className="text-xs text-muted-foreground">You can upload multiple images</p>
                <Button variant="outline" size="sm" className="mt-4" onClick={handleAddGalleryImage}>
                  Add More Images
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
