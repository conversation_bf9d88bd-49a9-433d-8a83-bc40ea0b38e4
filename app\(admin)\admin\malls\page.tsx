"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Plus, Search, Filter, Eye, Edit, Trash2, MoreHorizontal, MapPin, Store } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertD<PERSON>ogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Mall } from "@/lib/generated/prisma"
import { api, formatDate } from "@/lib"

const locations = ["All", "Downtown District", "Waterfront Area", "Innovation District", "Style Avenue"]
const statuses = ["All", "Active", "Inactive", "Maintenance"]

export default function AdminMallsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedLocation, setSelectedLocation] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid")
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [malls, setMalls] = useState<Mall[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchMalls = async () => {
      setIsLoading(true)
      try {
        const response = await fetch("/api/v1/malls")
        const data = await response.json()
        setMalls(data.data)
      } catch (error) {
        console.error("Error fetching malls:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchMalls()
  }, [])

  const filteredMalls = malls.filter((mall) => {
    const matchesSearch = mall.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mall.city?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesLocation = selectedLocation === "All" || mall.city === selectedLocation
    const matchesStatus = true

    return matchesSearch && matchesLocation && matchesStatus
  })

  const handleDelete = async (mallId: string) => {
    try {
      const response = api.delete(`/malls/${mallId}`)
      setMalls(malls.filter((mall) => mall.id !== mallId))
      alert(`Deleted mall: ${mallId}`)
      setDeleteId(null)
    } catch (error) {
      console.error("Error deleting mall:", error)
    }

  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Malls Management</h1>
          <p className="text-muted-foreground">
            Manage all shopping malls, their information, and settings.
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/malls/new">
            <Plus className="mr-2 h-4 w-4" />
            Add New Mall
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search malls..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedLocation} onValueChange={setSelectedLocation}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                Grid
              </Button>
              <Button
                variant={viewMode === "table" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("table")}
              >
                Table
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground">
          Showing {filteredMalls.length} of {malls.length} malls
        </p>
      </div>

      {/* Grid View */}
      {viewMode === "grid" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMalls.map((mall) => (
            <Card key={mall.id} className="overflow-hidden">
              <div className="relative">
                <Image
                  src={mall.images[0] || "/placeholder.svg"}
                  alt={mall.name}
                  width={150}
                  height={100}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute top-2 right-2">
                  <Badge variant={mall.state === "Active" ? "default" : "secondary"}>
                    {mall.state}
                  </Badge>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {mall.name}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/malls/${mall.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Public
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/malls/${mall.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/malls/${mall.id}/shops`}>
                          <Store className="mr-2 h-4 w-4" />
                          Manage Shops
                        </Link>
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete the mall
                              and all associated data.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(mall.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardTitle>
                <CardDescription className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {mall.city}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Shops:</span>
                  <span className="font-medium">{mall.totalShops}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Floors:</span>
                  <span className="font-medium">{mall.averageFootFall}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Rating:</span>
                  <span className="font-medium">{mall.rating}/5</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {/* {mall.categories.slice(0, 2).map((category) => (
                    <Badge key={category} variant="outline" className="text-xs">
                      {category}
                    </Badge>
                  ))}
                  {mall.categories.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{mall.categories.length - 2}
                    </Badge>
                  )} */}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Table View */}
      {viewMode === "table" && (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mall</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Shops</TableHead>
                <TableHead>Floors</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMalls.map((mall) => (
                <TableRow key={mall.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Image
                        src={mall.images[0] || "/placeholder.svg"}
                        alt={mall.name}
                        width={40}
                        height={40}
                        className="rounded object-cover"
                      />
                      <div>
                        <div className="font-medium">{mall.name}</div>
                        <div className="text-sm text-muted-foreground">{mall.address}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{mall.country}</TableCell>
                  <TableCell>{mall.totalShops}</TableCell>
                  {/* <TableCell>{mall.floors}</TableCell> */}
                  <TableCell>{mall.rating}/5</TableCell>
                  <TableCell>
                    <Badge variant={mall.state === "Active" ? "default" : "secondary"}>
                      {mall.state}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(mall.updatedAt)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/malls/${mall.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Public
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/malls/${mall.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/malls/${mall.id}/shops`}>
                            <Store className="mr-2 h-4 w-4" />
                            Manage Shops
                          </Link>
                        </DropdownMenuItem>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the mall
                                and all associated data.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(mall.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {filteredMalls.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg">No malls found matching your criteria.</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              setSearchTerm("")
              setSelectedLocation("All")
              setSelectedStatus("All")
            }}
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}
