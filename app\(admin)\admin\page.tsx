import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { getRecentMalls, getRecentReports, getRecentShops, getRecentUsers, getTotalActiveUsersAndPercentage, getTotalMallsAndDifference, getTotalPageViewsAndPercentage, getTotalShopsAndDifference, getTotalUsersAndDifference } from "@/lib/services/dashboard.service";
import { Building2, ShoppingBag, Users, TrendingUp, ArrowUpRight, ArrowDownRight, TrendingDown } from 'lucide-react'

export default async function AdminDashboard() {
  const [malls, shops, users, activeUsers, pageViews, recentMalls, recentShops, recentReports, recentUsers] = await Promise.all([
    getTotalMallsAndDifference(),
    getTotalShopsAndDifference(),
    getTotalUsersAndDifference(),
    getTotalActiveUsersAndPercentage(),
    getTotalPageViewsAndPercentage(),
    getRecentMalls(),
    getRecentShops(),
    getRecentReports(),
    getRecentUsers()
  ]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <p className="text-muted-foreground">Welcome to the MallExplorer admin dashboard.</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Malls</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{malls.total}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {/* <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              <span className="text-green-500">+2</span> from last month */}
              {malls.difference >= 0 ? (
                <>
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                  <span className="text-green-500">+{malls.difference} from last month</span>
                </>
              ) : (
                <>
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                  <span className="text-red-500">{malls.difference} from last month</span>
                </>
              )}

            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shops</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shops.total}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {/* <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              <span className="text-green-500">+12%</span> from last month */}
              {shops.difference >= 0 ? (
                <>
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                  <span className="text-green-500">+{shops.difference} from last month</span>
                </>
              ) : (
                <>
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                  <span className="text-red-500">{shops.difference} from last month</span>
                </>
              )}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeUsers.total}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {/* <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              <span className="text-green-500">+18%</span> from last month */}
              {activeUsers.percentageDifference >= 0 ? (
                <>
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                  <span className="text-green-500">+{activeUsers.percentageDifference}% from last month</span>
                </>
              ) : (
                <>
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                  <span className="text-red-500">{activeUsers.percentageDifference}% from last month</span>
                </>
              )}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Page Views</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pageViews.total}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {/* <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
              <span className="text-red-500">-3%</span> from last month */}
              {pageViews.percentageDifference >= 0 ? (
                <>
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                  <span className="text-green-500">+{pageViews.percentageDifference}% from last month</span>
                </>
              ) : (
                <>
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                  <span className="text-red-500">{pageViews.percentageDifference}% from last month</span>
                </>
              )}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Recent Malls</CardTitle>
                <CardDescription>Recently added shopping malls</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentMalls.map((mall, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{mall.name}</p>
                        <p className="text-xs text-muted-foreground">{mall.city}</p>
                      </div>
                      <p className="text-xs text-muted-foreground">{
                        new Date((mall as any).createdAt).toLocaleDateString()}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Recent Shops</CardTitle>
                <CardDescription>Recently added shops</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentShops.map((shop, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{shop.name}</p>
                        <p className="text-xs text-muted-foreground">{shop.mallName}</p>
                      </div>
                      <p className="text-xs text-muted-foreground">{new Date((shop as any).createdAt).toLocaleDateString()}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>User Activity</CardTitle>
                <CardDescription>Recent user registrations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentUsers.map((user, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                      <p className="text-xs text-muted-foreground">{
                        new Date((user as any).createdAt).toLocaleDateString()}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>View detailed analytics data</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center border-t">
              <p className="text-muted-foreground">Analytics charts will be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reports</CardTitle>
              <CardDescription>Generate and view reports</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center border-t">
              <p className="text-muted-foreground">Reports will be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
