import Image from "next/image"
import { MapPin, Star, Building, Store } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { MallDetailsPageData } from "@/lib/types/mall"
import { motion } from "motion/react"

interface MallHeaderProps {
  mall: MallDetailsPageData
  totalShops: number
  onShowMap: () => void
}

export function MallHeader({ mall, totalShops, onShowMap }: MallHeaderProps) {
  const floorCount = mall.floors?.length || 0

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
    >
      {/* Mall Image */}
      <div className="lg:h-[61vh] relative group">
        <Image
          src={mall.images?.[0] ?? "/placeholder.svg"}
          alt={mall.name}
          width={800}
          height={400}
          priority
          className="w-full h-full lg:h-full object-cover rounded-2xl shadow-2xl transition-transform duration-500 group-hover:scale-[1.02]"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Mall Information */}
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-2xl bg-gradient-to-br from-primary/10 to-secondary/10">
              <Building className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
                {mall.name}
              </h1>
            </div>
          </div>

          <div className="flex items-center gap-2 text-muted-foreground">
            <MapPin className="h-5 w-5 text-primary" />
            <span className="text-lg">{mall.address}</span>
            <Button className="ml-auto" variant="outline" onClick={onShowMap}>
              <MapPin className="h-4 w-4 mr-2" />
              View Map
            </Button>
          </div>

          <div className="flex items-center gap-4 flex-wrap">
            {mall.rating && (
              <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-lg flex items-center gap-1 px-3 py-1">
                <Star className="h-4 w-4 fill-current" />
                {mall.rating}
              </Badge>
            )}
            <Badge variant="outline" className="bg-background/50 px-3 py-1">
              <Building className="h-3 w-3 mr-1" />
              {floorCount} Floors
            </Badge>
            <Badge variant="outline" className="bg-background/50 px-3 py-1">
              <Store className="h-3 w-3 mr-1" />
              {totalShops} Stores
            </Badge>
          </div>
        </div>

        <MallInfoCard mall={mall} />
        <MallAmenities amenities={mall.amenities} />
      </div>
    </motion.div>
  )
}

function MallInfoCard({ mall }: { mall: MallDetailsPageData }) {
  return (
    <Card className="bg-gradient-to-br from-background to-muted/30 border-0 shadow-lg backdrop-blur-sm">
      <CardContent className="p-6 space-y-4">
        <p className="text-muted-foreground leading-relaxed">{mall.description}</p>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="flex items-center gap-3 p-3 rounded-xl bg-muted/30">
            <div className="h-5 w-5 text-primary">🕒</div>
            <div>
              <p className="text-sm font-medium">Opening Hours</p>
              <p className="text-sm text-muted-foreground">
                {mall.mallHours?.[0]?.open ?? "N/A"} - {mall.mallHours?.[0]?.close ?? "N/A"}
              </p>
            </div>
          </div>

          {mall.phone && (
            <div className="flex items-center gap-3 p-3 rounded-xl bg-muted/30">
              <div className="h-5 w-5 text-primary">📞</div>
              <div>
                <p className="text-sm font-medium">Contact</p>
                <p className="text-sm text-muted-foreground">{mall.phone}</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function MallAmenities({ amenities }: { amenities?: string[] }) {
  if (!amenities?.length) return null

  return (
    <Card className="bg-gradient-to-br from-background to-muted/30 border-0 shadow-lg backdrop-blur-sm">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="h-5 w-5 text-primary">✨</div>
          <h3 className="font-semibold">Amenities</h3>
        </div>
        <div className="flex flex-wrap gap-2">
          {amenities.map((amenity, index) => (
            <Badge key={index} variant="outline" className="bg-background/50 text-sm px-3 py-1">
              {amenity}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
