'use client'
import { useState, useRef, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import * as Select from '@radix-ui/react-select';
import * as Tabs from '@radix-ui/react-tabs';
import * as Toast from '@radix-ui/react-toast';
import { X, Download, Upload, Plus, Trash2, Settings, Info, ChevronDown, Check } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';

interface Shop {
    id: number;
    name: string;
    x: number;
    y: number;
    width: number;
    height: number;
    category: string;
}

interface FloorPlan {
    name: string;
    width: number;
    height: number;
    shops: Shop[];
}

const DEFAULT_CATEGORIES = [
    'clothing', 'electronics', 'food', 'books',
    'sports', 'beauty', 'gifts', 'health'
];

const DEFAULT_COLORS: Record<string, string> = {
    clothing: '#3b82f6',
    electronics: '#8b5cf6',
    food: '#ef4444',
    books: '#f59e0b',
    sports: '#10b981',
    beauty: '#ec4899',
    gifts: '#d946ef',
    health: '#06b6d4'
};

const MallFloorPlanCreator = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [floorPlan, setFloorPlan] = useState<FloorPlan>({
        name: 'New Mall Floor',
        width: 800,
        height: 600,
        shops: []
    });
    const [selectedShop, setSelectedShop] = useState<Shop | null>(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
    const [startPos, setStartPos] = useState({ x: 0, y: 0 });
    const [currentCategory, setCurrentCategory] = useState('clothing');
    const [shopName, setShopName] = useState('New Shop');
    const [showExportModal, setShowExportModal] = useState(false);
    const [showImportModal, setShowImportModal] = useState(false);
    const [exportData, setExportData] = useState('');
    const [importData, setImportData] = useState('');
    const [toastOpen, setToastOpen] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    // Initialize canvas
    useEffect(() => {
        drawCanvas();
    }, [floorPlan, selectedShop]);

    const showToast = (message: string) => {
        setToastMessage(message);
        setToastOpen(true);
    };

    const drawCanvas = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Clear canvas with gradient background
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#f8fafc');
        gradient.addColorStop(1, '#f1f5f9');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw subtle grid
        ctx.strokeStyle = '#e2e8f0';
        ctx.lineWidth = 0.5;
        const gridSize = 20;

        for (let x = 0; x < canvas.width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
        }

        for (let y = 0; y < canvas.height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
        }

        // Draw shops with enhanced styling
        floorPlan.shops.forEach(shop => {
            const isSelected = selectedShop?.id === shop.id;
            const color = DEFAULT_COLORS[shop.category] || '#64748b';

            // Shadow for depth
            if (!isSelected) {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx.beginPath();
                ctx.roundRect(shop.x + 2, shop.y + 2, shop.width, shop.height, 8);
                ctx.fill();
            }

            // Main shop rectangle
            ctx.fillStyle = color + '20';
            ctx.strokeStyle = isSelected ? color : color + '80';
            ctx.lineWidth = isSelected ? 3 : 2;

            ctx.beginPath();
            ctx.roundRect(shop.x, shop.y, shop.width, shop.height, 8);
            ctx.fill();
            ctx.stroke();

            // Selected shop glow effect
            if (isSelected) {
                ctx.strokeStyle = color + '40';
                ctx.lineWidth = 6;
                ctx.beginPath();
                ctx.roundRect(shop.x - 2, shop.y - 2, shop.width + 4, shop.height + 4, 10);
                ctx.stroke();
            }

            // Shop name with better typography
            ctx.fillStyle = '#1e293b';
            ctx.font = 'bold 13px Inter, system-ui, sans-serif';
            ctx.textAlign = 'center';
            wrapText(ctx, shop.name, shop.x + shop.width / 2, shop.y + shop.height / 2 - 8, shop.width - 16, 18);

            // Category label
            ctx.fillStyle = color;
            ctx.font = '10px Inter, system-ui, sans-serif';
            ctx.fillText(shop.category.toUpperCase(), shop.x + shop.width / 2, shop.y + shop.height / 2 + 12);
        });

        // Draw preview while drawing
        if (isDrawing) {
            const width = Math.abs(startPos.x - floorPlan.width);
            const height = Math.abs(startPos.y - floorPlan.height);
            const color = DEFAULT_COLORS[currentCategory];

            ctx.fillStyle = color + '15';
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.setLineDash([8, 4]);
            ctx.beginPath();
            ctx.roundRect(
                Math.min(startPos.x, floorPlan.width),
                Math.min(startPos.y, floorPlan.height),
                width,
                height,
                8
            );
            ctx.fill();
            ctx.stroke();
            ctx.setLineDash([]);

            // Dimensions display
            ctx.fillStyle = color;
            ctx.font = 'bold 12px Inter, system-ui, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillRect(startPos.x + width / 2 - 30, startPos.y - 25, 60, 20);
            ctx.fillStyle = 'white';
            ctx.fillText(`${width}×${height}`, startPos.x + width / 2, startPos.y - 10);
        }
    };

    const wrapText = (
        ctx: CanvasRenderingContext2D,
        text: string,
        x: number,
        y: number,
        maxWidth: number,
        lineHeight: number
    ) => {
        const words = text.split(' ');
        let line = '';
        let lines: string[] = [];

        for (let n = 0; n < words.length; n++) {
            const testLine = line + words[n] + ' ';
            const metrics = ctx.measureText(testLine);
            if (metrics.width > maxWidth && n > 0) {
                lines.push(line.trim());
                line = words[n] + ' ';
            } else {
                line = testLine;
            }
        }
        lines.push(line.trim());

        for (let i = 0; i < lines.length; i++) {
            ctx.fillText(lines[i], x, y + (i - (lines.length - 1) / 2) * lineHeight);
        }
    };

    const getCanvasCoordinates = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (!canvas) return { x: 0, y: 0 };

        const rect = canvas.getBoundingClientRect();
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;

        return {
            x: Math.floor((e.clientX - rect.left) * scaleX),
            y: Math.floor((e.clientY - rect.top) * scaleY)
        };
    };

    const handleCanvasMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const { x, y } = getCanvasCoordinates(e);

        // Check if clicking on existing shop
        const clickedShop = floorPlan.shops.find(shop =>
            x >= shop.x && x <= shop.x + shop.width &&
            y >= shop.y && y <= shop.y + shop.height
        );

        if (clickedShop) {
            setSelectedShop(clickedShop);
            setIsDragging(true);
            setDragOffset({
                x: x - clickedShop.x,
                y: y - clickedShop.y
            });
            return;
        }

        // Start drawing new shop
        setSelectedShop(null);
        setIsDrawing(true);
        setStartPos({ x, y });
        setFloorPlan(prev => ({ ...prev, width: x, height: y }));
    };

    const handleCanvasMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const { x, y } = getCanvasCoordinates(e);

        if (isDragging && selectedShop) {
            // Move the selected shop
            const newX = x - dragOffset.x;
            const newY = y - dragOffset.y;

            // Ensure shop stays within canvas bounds
            const boundedX = Math.max(0, Math.min(newX, 800 - selectedShop.width));
            const boundedY = Math.max(0, Math.min(newY, 600 - selectedShop.height));

            updateShop({ x: boundedX, y: boundedY });
            return;
        }

        if (isDrawing) {
            setFloorPlan(prev => ({ ...prev, width: x, height: y }));
        }
    };

    const handleCanvasMouseUp = () => {
        if (isDragging) {
            setIsDragging(false);
            return;
        }

        if (!isDrawing) return;
        setIsDrawing(false);

        const width = Math.abs(startPos.x - floorPlan.width);
        const height = Math.abs(startPos.y - floorPlan.height);

        // Only create shop if it's large enough
        if (width < 40 || height < 40) {
            showToast('Shop must be at least 40×40 pixels');
            return;
        }

        const newShop: Shop = {
            id: Date.now(),
            name: shopName,
            x: Math.min(startPos.x, floorPlan.width),
            y: Math.min(startPos.y, floorPlan.height),
            width,
            height,
            category: currentCategory
        };

        setFloorPlan(prev => ({
            ...prev,
            shops: [...prev.shops, newShop]
        }));

        setSelectedShop(newShop);
        showToast(`${shopName} added successfully!`);
    };

    const updateShop = (updates: Partial<Shop>) => {
        if (!selectedShop) return;

        const updatedShops = floorPlan.shops.map(shop =>
            shop.id === selectedShop.id ? { ...shop, ...updates } : shop
        );

        setFloorPlan(prev => ({ ...prev, shops: updatedShops }));
        setSelectedShop(prev => prev ? { ...prev, ...updates } : null);
    };

    const deleteShop = () => {
        if (!selectedShop) return;

        const updatedShops = floorPlan.shops.filter(shop => shop.id !== selectedShop.id);
        setFloorPlan(prev => ({ ...prev, shops: updatedShops }));
        setSelectedShop(null);
        showToast('Shop deleted successfully');
    };

    const exportFloorPlan = () => {
        const data = JSON.stringify(floorPlan, null, 2);
        setExportData(data);
        setShowExportModal(true);
    };

    const importFloorPlan = () => {
        try {
            const parsed = JSON.parse(importData);
            setFloorPlan(parsed);
            setShowImportModal(false);
            setImportData('');
            showToast('Floor plan imported successfully!');
        } catch (e) {
            showToast('Invalid JSON data. Please check your input.');
        }
    };

    const copyToClipboard = () => {
        navigator.clipboard.writeText(exportData);
        showToast('Floor plan copied to clipboard!');
        setShowExportModal(false);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
            <Toast.Provider swipeDirection="right">
                <div className="max-w-7xl mx-auto p-6">
                    {/* Header */}
                    <div className="mb-8">
                        <h1 className="text-4xl font-bold text-slate-800 mb-2">Mall Floor Plan Creator</h1>
                        <p className="text-slate-600">Design and manage your mall layout with ease</p>
                    </div>

                    <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                        {/* Enhanced Controls Panel */}
                        <div className="xl:col-span-1">
                            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <Tabs.Root defaultValue="design" className="w-full">
                                    <Tabs.List className="grid w-full grid-cols-2 mb-6 bg-slate-100 rounded-lg p-1">
                                        <Tabs.Trigger
                                            value="design"
                                            className="px-3 py-2 text-sm font-medium rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all"
                                        >
                                            <Plus className="w-4 h-4 mr-2" />
                                            Design
                                        </Tabs.Trigger>
                                        <Tabs.Trigger
                                            value="manage"
                                            className="px-3 py-2 text-sm font-medium rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all"
                                        >
                                            <Settings className="w-4 h-4 mr-2" />
                                            Manage
                                        </Tabs.Trigger>
                                    </Tabs.List>

                                    <Tabs.Content value="design" className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Floor Name</label>
                                            <input
                                                type="text"
                                                value={floorPlan.name}
                                                onChange={(e) => setFloorPlan(prev => ({ ...prev, name: e.target.value }))}
                                                className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                placeholder="Enter floor name"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">New Shop Name</label>
                                            <input
                                                type="text"
                                                value={shopName}
                                                onChange={(e) => setShopName(e.target.value)}
                                                className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                placeholder="Enter shop name"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Shop Category</label>
                                            <Select.Root value={currentCategory} onValueChange={setCurrentCategory}>
                                                <Select.Trigger className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all flex items-center justify-between">
                                                    <Select.Value />
                                                    <ChevronDown className="w-4 h-4" />
                                                </Select.Trigger>
                                                <Select.Portal>
                                                    <Select.Content className="bg-white rounded-lg shadow-xl border border-slate-200 p-1 z-50">
                                                        <Select.Viewport>
                                                            {DEFAULT_CATEGORIES.map(category => (
                                                                <Select.Item
                                                                    key={category}
                                                                    value={category}
                                                                    className="px-3 py-2 text-sm rounded-md cursor-pointer hover:bg-slate-100 flex items-center justify-between data-[highlighted]:bg-slate-100"
                                                                >
                                                                    <div className="flex items-center">
                                                                        <div
                                                                            className="w-3 h-3 rounded-full mr-3"
                                                                            style={{ backgroundColor: DEFAULT_COLORS[category] }}
                                                                        />
                                                                        <Select.ItemText className="capitalize">{category}</Select.ItemText>
                                                                    </div>
                                                                    <Select.ItemIndicator>
                                                                        <Check className="w-4 h-4" />
                                                                    </Select.ItemIndicator>
                                                                </Select.Item>
                                                            ))}
                                                        </Select.Viewport>
                                                    </Select.Content>
                                                </Select.Portal>
                                            </Select.Root>
                                        </div>

                                        <div className="bg-slate-50 p-4 rounded-lg">
                                            <div className="flex items-center text-blue-600 mb-2">
                                                <Info className="w-4 h-4 mr-2" />
                                                <span className="text-sm font-medium">How to Use</span>
                                            </div>
                                            <ul className="text-xs text-slate-600 space-y-1">
                                                <li>• Click and drag to create new shops</li>
                                                <li>• Click and drag existing shops to move them</li>
                                                <li>• Click shops to select them</li>
                                                <li>• Use the Manage tab to edit selected shops</li>
                                                <li>• Minimum shop size: 40×40 pixels</li>
                                            </ul>
                                        </div>
                                    </Tabs.Content>

                                    <Tabs.Content value="manage" className="space-y-4">
                                        {selectedShop ? (
                                            <motion.div
                                                initial={{ opacity: 0, y: 10 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.2 }}
                                                className="space-y-4"
                                            >
                                                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                                    <h3 className="font-medium text-blue-900 mb-3">Selected Shop</h3>
                                                    <div className="space-y-3">
                                                        <div>
                                                            <label className="block text-xs font-medium text-blue-700 mb-1">Name</label>
                                                            <input
                                                                type="text"
                                                                value={selectedShop.name}
                                                                onChange={(e) => updateShop({ name: e.target.value })}
                                                                className="w-full px-3 py-2 text-sm border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                            />
                                                        </div>

                                                        <div className="grid grid-cols-2 gap-2">
                                                            <div>
                                                                <label className="block text-xs font-medium text-blue-700 mb-1">X Position</label>
                                                                <input
                                                                    type="number"
                                                                    value={selectedShop.x}
                                                                    onChange={(e) => updateShop({ x: parseInt(e.target.value) || 0 })}
                                                                    className="w-full px-2 py-1 text-sm border border-blue-200 rounded-md"
                                                                />
                                                            </div>
                                                            <div>
                                                                <label className="block text-xs font-medium text-blue-700 mb-1">Y Position</label>
                                                                <input
                                                                    type="number"
                                                                    value={selectedShop.y}
                                                                    onChange={(e) => updateShop({ y: parseInt(e.target.value) || 0 })}
                                                                    className="w-full px-2 py-1 text-sm border border-blue-200 rounded-md"
                                                                />
                                                            </div>
                                                        </div>

                                                        <div className="grid grid-cols-2 gap-2">
                                                            <div>
                                                                <label className="block text-xs font-medium text-blue-700 mb-1">Width</label>
                                                                <input
                                                                    type="number"
                                                                    value={selectedShop.width}
                                                                    onChange={(e) => updateShop({ width: Math.max(40, parseInt(e.target.value) || 40) })}
                                                                    className="w-full px-2 py-1 text-sm border border-blue-200 rounded-md"
                                                                    min="40"
                                                                />
                                                            </div>
                                                            <div>
                                                                <label className="block text-xs font-medium text-blue-700 mb-1">Height</label>
                                                                <input
                                                                    type="number"
                                                                    value={selectedShop.height}
                                                                    onChange={(e) => updateShop({ height: Math.max(40, parseInt(e.target.value) || 40) })}
                                                                    className="w-full px-2 py-1 text-sm border border-blue-200 rounded-md"
                                                                    min="40"
                                                                />
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <label className="block text-xs font-medium text-blue-700 mb-1">Category</label>
                                                            <Select.Root value={selectedShop.category} onValueChange={(value) => updateShop({ category: value })}>
                                                                <Select.Trigger className="w-full px-3 py-2 text-sm border border-blue-200 rounded-md flex items-center justify-between">
                                                                    <Select.Value />
                                                                    <ChevronDown className="w-3 h-3" />
                                                                </Select.Trigger>
                                                                <Select.Portal>
                                                                    <Select.Content className="bg-white rounded-lg shadow-xl border border-slate-200 p-1 z-50">
                                                                        <Select.Viewport>
                                                                            {DEFAULT_CATEGORIES.map(category => (
                                                                                <Select.Item
                                                                                    key={category}
                                                                                    value={category}
                                                                                    className="px-3 py-2 text-sm rounded-md cursor-pointer hover:bg-slate-100 flex items-center justify-between data-[highlighted]:bg-slate-100"
                                                                                >
                                                                                    <div className="flex items-center">
                                                                                        <div
                                                                                            className="w-3 h-3 rounded-full mr-3"
                                                                                            style={{ backgroundColor: DEFAULT_COLORS[category] }}
                                                                                        />
                                                                                        <Select.ItemText className="capitalize">{category}</Select.ItemText>
                                                                                    </div>
                                                                                    <Select.ItemIndicator>
                                                                                        <Check className="w-4 h-4" />
                                                                                    </Select.ItemIndicator>
                                                                                </Select.Item>
                                                                            ))}
                                                                        </Select.Viewport>
                                                                    </Select.Content>
                                                                </Select.Portal>
                                                            </Select.Root>
                                                        </div>

                                                        <button
                                                            onClick={deleteShop}
                                                            className="w-full mt-3 px-4 py-2 bg-red-100 text-red-700 text-sm rounded-lg hover:bg-red-200 transition-colors flex items-center justify-center"
                                                        >
                                                            <Trash2 className="w-4 h-4 mr-2" />
                                                            Delete Shop
                                                        </button>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        ) : (
                                            <motion.div
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                className="bg-slate-50 p-6 rounded-lg text-center"
                                            >
                                                <div className="text-slate-400 mb-3">
                                                    <Settings className="w-8 h-8 mx-auto" />
                                                </div>
                                                <p className="text-slate-600 text-sm">Select a shop to manage its properties</p>
                                            </motion.div>
                                        )}

                                        <div className="border-t pt-4 space-y-2">
                                            <button
                                                onClick={exportFloorPlan}
                                                className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                                            >
                                                <Download className="w-4 h-4 mr-2" />
                                                Export Floor Plan
                                            </button>
                                            <button
                                                onClick={() => setShowImportModal(true)}
                                                className="w-full px-4 py-3 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition-colors flex items-center justify-center"
                                            >
                                                <Upload className="w-4 h-4 mr-2" />
                                                Import Floor Plan
                                            </button>
                                        </div>
                                    </Tabs.Content>
                                </Tabs.Root>
                            </div>
                        </div>

                        {/* Enhanced Canvas Area */}
                        <div className="xl:col-span-3 space-y-6">
                            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div className="flex justify-between items-center mb-6">
                                    <div>
                                        <h2 className="text-2xl font-semibold text-slate-800">{floorPlan.name}</h2>
                                        <p className="text-slate-600 text-sm mt-1">
                                            {floorPlan.shops.length} shops • 800×600 canvas
                                        </p>
                                    </div>
                                    <div className="text-sm text-slate-500 bg-slate-100 px-3 py-1 rounded-full">
                                        {isDrawing ? 'Drawing...' : isDragging ? 'Moving...' : 'Ready'}
                                    </div>
                                </div>

                                <div className="border-2 border-slate-200 rounded-xl overflow-hidden shadow-inner bg-gradient-to-br from-slate-50 to-white">
                                    <canvas
                                        ref={canvasRef}
                                        width={800}
                                        height={600}
                                        onMouseDown={handleCanvasMouseDown}
                                        onMouseMove={handleCanvasMouseMove}
                                        onMouseUp={handleCanvasMouseUp}
                                        onMouseLeave={handleCanvasMouseUp}
                                        className="w-full cursor-crosshair touch-none"
                                    />
                                </div>

                                <div className="mt-4 flex items-center justify-between text-sm text-slate-500">
                                    <span>
                                        {isDrawing ? `Creating ${shopName} (${currentCategory})` :
                                            isDragging ? `Moving ${selectedShop?.name}` :
                                                'Click and drag to add shops • Click and drag to move shops'}
                                    </span>
                                    <div className="flex items-center space-x-4">
                                        <span>Current category:</span>
                                        <div className="flex items-center">
                                            <div
                                                className="w-3 h-3 rounded-full mr-2"
                                                style={{ backgroundColor: DEFAULT_COLORS[currentCategory] }}
                                            />
                                            <span className="capitalize font-medium">{currentCategory}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Enhanced Shops List */}
                            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <h3 className="text-xl font-semibold text-slate-800 mb-4">Shops Overview</h3>
                                {floorPlan.shops.length === 0 ? (
                                    <motion.div
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        className="text-center py-12"
                                    >
                                        <div className="text-slate-300 mb-4">
                                            <Plus className="w-12 h-12 mx-auto" />
                                        </div>
                                        <p className="text-slate-500">No shops yet</p>
                                        <p className="text-slate-400 text-sm mt-1">Start by drawing on the canvas above</p>
                                    </motion.div>
                                ) : (
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {floorPlan.shops.map(shop => (
                                            <motion.div
                                                key={shop.id}
                                                initial={{ opacity: 0, y: 10 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.2 }}
                                                onClick={() => setSelectedShop(shop)}
                                                className={`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${selectedShop?.id === shop.id
                                                        ? 'border-blue-500 bg-blue-50 shadow-lg'
                                                        : 'border-slate-200 hover:border-slate-300 bg-white'
                                                    }`}
                                            >
                                                <div className="flex items-start justify-between mb-2">
                                                    <div className="font-medium text-slate-800 leading-tight">{shop.name}</div>
                                                    <div
                                                        className="w-4 h-4 rounded-full flex-shrink-0 ml-2"
                                                        style={{ backgroundColor: DEFAULT_COLORS[shop.category] }}
                                                    />
                                                </div>
                                                <div className="text-xs text-slate-500 space-y-1">
                                                    <div>{shop.width}×{shop.height}px</div>
                                                    <div>Position: ({shop.x}, {shop.y})</div>
                                                    <div className="capitalize font-medium text-slate-600">{shop.category}</div>
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Export Modal */}
                <Dialog.Root open={showExportModal} onOpenChange={setShowExportModal}>
                    <Dialog.Portal>
                        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
                        <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-auto shadow-2xl z-50">
                            <div className="flex items-center justify-between mb-4">
                                <Dialog.Title className="text-xl font-semibold">Export Floor Plan</Dialog.Title>
                                <Dialog.Close className="p-2 hover:bg-slate-100 rounded-lg transition-colors">
                                    <X className="w-5 h-5" />
                                </Dialog.Close>
                            </div>
                            <textarea
                                value={exportData}
                                readOnly
                                className="w-full h-64 p-4 border border-slate-300 rounded-lg font-mono text-sm bg-slate-50 resize-none"
                                placeholder="Floor plan JSON will appear here"
                            />
                            <div className="flex justify-end space-x-3 mt-4">
                                <Dialog.Close className="px-4 py-2 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition-colors">
                                    Cancel
                                </Dialog.Close>
                                <button
                                    onClick={copyToClipboard}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                                >
                                    <Download className="w-4 h-4 mr-2" />
                                    Copy to Clipboard
                                </button>
                            </div>
                        </Dialog.Content>
                    </Dialog.Portal>
                </Dialog.Root>

                {/* Import Modal */}
                <Dialog.Root open={showImportModal} onOpenChange={setShowImportModal}>
                    <Dialog.Portal>
                        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
                        <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-auto shadow-2xl z-50">
                            <div className="flex items-center justify-between mb-4">
                                <Dialog.Title className="text-xl font-semibold">Import Floor Plan</Dialog.Title>
                                <Dialog.Close className="p-2 hover:bg-slate-100 rounded-lg transition-colors">
                                    <X className="w-5 h-5" />
                                </Dialog.Close>
                            </div>
                            <textarea
                                value={importData}
                                onChange={(e) => setImportData(e.target.value)}
                                className="w-full h-64 p-4 border border-slate-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Paste your floor plan JSON here..."
                            />
                            <div className="flex justify-end space-x-3 mt-4">
                                <Dialog.Close className="px-4 py-2 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition-colors">
                                    Cancel
                                </Dialog.Close>
                                <button
                                    onClick={importFloorPlan}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                                    disabled={!importData.trim()}
                                >
                                    <Upload className="w-4 h-4 mr-2" />
                                    Import Floor Plan
                                </button>
                            </div>
                        </Dialog.Content>
                    </Dialog.Portal>
                </Dialog.Root>

                {/* Toast Notifications */}
                <AnimatePresence>
                    {toastOpen && (
                        <Toast.Root asChild forceMount>
                            <motion.div
                                initial={{ x: '100%' }}
                                animate={{ x: 0 }}
                                exit={{ x: '100%' }}
                                transition={{ type: 'spring', damping: 20, stiffness: 300 }}
                                className="bg-white border border-slate-200 rounded-lg shadow-lg p-4 flex items-center space-x-3"
                            >
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <Toast.Description className="text-slate-700 font-medium">
                                    {toastMessage}
                                </Toast.Description>
                                <Toast.Close
                                    onClick={() => setToastOpen(false)}
                                    className="ml-auto p-1 hover:bg-slate-100 rounded transition-colors"
                                >
                                    <X className="w-4 h-4" />
                                </Toast.Close>
                            </motion.div>
                        </Toast.Root>
                    )}
                </AnimatePresence>
                <Toast.Viewport className="fixed bottom-0 right-0 flex flex-col p-6 gap-2 w-96 max-w-[100vw] m-0 list-none z-50 outline-none" />
            </Toast.Provider>
        </div>
    );
};

export default MallFloorPlanCreator;