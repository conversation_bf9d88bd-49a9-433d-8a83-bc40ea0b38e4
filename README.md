# Mall Explorer API

A comprehensive REST API for exploring shopping malls, shops, and managing user accounts. Built with Next.js 15, TypeScript, Prisma ORM, and JWT authentication.

## 🚀 Features

- **Mall Management**: Browse, search, and filter shopping malls
- **Shop Discovery**: Explore shops within malls with detailed information
- **User Authentication**: Secure JWT-based authentication with refresh tokens
- **Admin Dashboard**: Administrative interface for managing malls and shops
- **Real-time Data**: Live statistics and analytics
- **Responsive Design**: Mobile-first approach with Tailwind CSS

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Database**: Prisma ORM with PostgreSQL/MySQL
- **Authentication**: JWT with refresh tokens
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Validation**: Zod schemas
- **UI Components**: Radix UI primitives

## 📋 Prerequisites

- Node.js 18+ 
- npm/yarn/pnpm
- Database (PostgreSQL/MySQL)

## ⚡ Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mall-explorer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   DATABASE_URL="your-database-connection-string"
   JWT_SECRET="your-super-secret-jwt-key"
   JWT_REFRESH_SECRET="your-super-secret-refresh-key"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Seed the database (optional)**
   ```bash
   npm run seed
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:3000/api/v1`

## 📚 API Documentation

### Base URL
```
http://localhost:3000/api/v1
```

### Response Format
All API responses follow this consistent format:
```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "error": string,
  "meta": {
    "page": number,
    "limit": number,
    "total": number,
    "hasNext": boolean
  }
}
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication with the following approach:

### Token Types
- **Access Token**: Short-lived (15 minutes), used for API requests
- **Refresh Token**: Long-lived (7 days), used to refresh access tokens

### Authentication Flow
1. Login with credentials to receive both tokens
2. Include access token in requests via HTTP-only cookies
3. When access token expires, use refresh token to get new tokens

### Token Storage
- Tokens are stored as HTTP-only cookies for security
- Access token cookie expires in 15 minutes
- Refresh token cookie expires in 7 days

### Authorization Levels
- **Public**: No authentication required
- **User**: Requires valid access token
- **Admin**: Requires valid access token + admin role

## 🔑 JWT Payload Structure
```json
{
  "userId": "string",
  "email": "string", 
  "role": "user" | "admin",
  "iat": number,
  "exp": number
}
```

## 📖 API Endpoints

### 🔓 Public Endpoints (No Authentication Required)

#### Authentication
| Method | Endpoint | Description | Returns |
|--------|----------|-------------|---------|
| POST | `/auth/register` | Register a new user | `AuthResponse` |
| POST | `/auth/login` | Login user | `AuthResponse` |
| POST | `/auth/refresh-token` | Refresh access token | `TokenResponse` |
| GET | `/auth/logout` | Logout user (clears cookies) | `void` |

#### Malls
| Method | Endpoint | Description | Returns |
|--------|----------|-------------|---------|
| GET | `/malls` | Get all malls with filtering | `Mall[]` with pagination |
| GET | `/malls/featured` | Get featured malls | `FeaturedMallData[]` |
| GET | `/malls/{slug}` | Get mall by slug | `MallDetailsPageData` |
| GET | `/malls/{slug}/shops` | Get shops in a specific mall | `Shop[]` with pagination |

#### Shops
| Method | Endpoint | Description | Returns |
|--------|----------|-------------|---------|
| GET | `/shops` | Get all shops with filtering | `Shop[]` |
| GET | `/shops/{id}` | Get shop by ID | `Shop` |
| GET | `/shops/floor?id={floorId}` | Get shops by floor ID | `Shop[]` |

#### Floors
| Method | Endpoint | Description | Returns |
|--------|----------|-------------|---------|
| GET | `/floors/{mallId}` | Get floors by mall ID | `Floor[]` |
| GET | `/floors/{id}` | Get floor by ID | `Floor` |

#### Categories & Amenities
| Method | Endpoint | Description | Returns |
|--------|----------|-------------|---------|
| GET | `/categories` | Get all shop categories | `string[]` |
| GET | `/amenities` | Get all mall amenities | `string[]` |

### 🔒 User Endpoints (Authentication Required)

#### User Profile
| Method | Endpoint | Description | Auth Level | Returns |
|--------|----------|-------------|------------|---------|
| GET | `/users/profile` | Get user profile | User | `User` |
| PUT | `/users/profile` | Update user profile | User | `User` |

### 🛡️ Admin Endpoints (Admin Authentication Required)

#### Dashboard
| Method | Endpoint | Description | Auth Level | Returns |
|--------|----------|-------------|------------|---------|
| GET | `/admin/dashboard` | Get admin dashboard data | Admin | `DashboardData` |

#### Mall Management
| Method | Endpoint | Description | Auth Level | Returns |
|--------|----------|-------------|------------|---------|
| POST | `/malls` | Create new mall | Admin | `Mall` |
| PUT | `/malls/{slug}` | Update mall | Admin | `Mall` |
| DELETE | `/malls/{slug}` | Delete mall | Admin | `Mall` |

#### Shop Management
| Method | Endpoint | Description | Auth Level | Returns |
|--------|----------|-------------|------------|---------|
| POST | `/shops` | Create new shop | Admin | `Shop` |
| PUT | `/shops/{id}` | Update shop | Admin | `Shop` |
| DELETE | `/shops/{id}` | Delete shop | Admin | `Shop` |

#### Floor Management
| Method | Endpoint | Description | Auth Level | Returns |
|--------|----------|-------------|------------|---------|
| POST | `/floors/{mallId}` | Create new floor | Admin | `Floor` |
| PUT | `/floors/{id}` | Update floor | Admin | `Floor` |
| DELETE | `/floors/{id}` | Delete floor | Admin | `Floor` |

## 📝 Request Examples

### Register User
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "phone": "+1234567890"
}
```

### Login User
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### Get Malls with Filters
```bash
GET /api/v1/malls?page=1&limit=10&searchTerm=central&city=New York&activeOnly=true
```

### Create Mall (Admin Only)
```bash
POST /api/v1/malls
Content-Type: application/json
Cookie: token=your-jwt-token

{
  "name": "Central Mall",
  "slug": "central-mall",
  "description": "A modern shopping center",
  "address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "country": "USA",
  "amenities": ["parking", "food-court", "wifi"]
}
```

### Get Shops by Floor
```bash
GET /api/v1/shops/floor?id=floor-uuid-here
```

### Create Floor (Admin Only)
```bash
POST /api/v1/floors/mall-uuid-here
Content-Type: application/json
Cookie: token=your-jwt-token

{
  "number": 1,
  "name": "Ground Floor",
  "mapImage": "https://example.com/floor-map.jpg",
  "amenities": ["restrooms", "elevators"],
  "categories": ["fashion", "electronics"]
}
```

## 🔍 Query Parameters

### Pagination
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

### Filtering (Malls)
- `searchTerm`: Search in name/description
- `category`: Filter by category
- `city`: Filter by city
- `state`: Filter by state
- `country`: Filter by country
- `amenities`: Filter by amenities (array)
- `activeOnly`: Show only active malls (boolean)
- `sortBy`: Sort field (name, createdAt, etc.)

### Filtering (Shops)
- `mallId`: Filter by mall ID
- `floor`: Filter by floor
- `category`: Filter by shop category
- `searchTerm`: Search in shop name/description

### Filtering (Floors)
- `mallId`: Filter floors by mall ID (used in path parameter)

## 📊 Data Types

### Core Data Models

#### User
```typescript
{
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  role: "user" | "admin" | "manager"
  isActive: boolean
  lastLoginAt?: Date
  preferences?: object
  createdAt: Date
  updatedAt: Date
}
```

#### Mall
```typescript
{
  id: string
  name: string
  slug: string
  description?: string
  shortDescription?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  phone?: string
  email?: string
  website?: string
  images: string[]
  logo?: string
  amenities: string[]
  rating?: number
  latitude?: number
  longitude?: number
  parkingAvailable: boolean
  parkingCapacity?: number
  parkingType?: "free" | "paid" | "valet"
  parkingRates?: string
  totalShops?: number
  averageFootFall?: number
  yearOpened?: number
  company?: string
  manager?: string
  contactPerson?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}
```

#### Shop
```typescript
{
  id: string
  category: string
  mallId: string
  name: string
  description?: string
  phone?: string
  images: string[]
  email?: string
  website?: string
  floor: number
  wing?: string
  coordinates?: string
  socialMedia?: object
  image?: string
  logo?: string
  offerId?: string
  featured: boolean
  specialFeatures: string[]
  rating?: number
  tags: string[]
  openingDate?: Date
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}
```

#### Floor
```typescript
{
  id: string
  mallId: string
  number: number
  name: string
  mapImage?: string
  amenities: string[]
  categories: string[]
  createdAt: Date
  updatedAt: Date
}
```

### Response Types

#### AuthResponse
```typescript
{
  success: true
  data: {
    user: {
      id: string
      name: string
      email: string
      role: "user" | "admin" | "manager"
    }
    accessToken: string
    refreshToken: string
  }
}
```

#### TokenResponse
```typescript
{
  accessToken: string
  refreshToken: string
}
```

#### FeaturedMallData
```typescript
{
  id: string
  name: string
  city?: string
  state?: string
  address?: string
  country?: string
  shortDescription?: string
  image?: string
  averageFootFall?: number
  totalShops?: number
  totalFloors: number
  categories: string[]
  slug: string
  logo?: string
  rating?: number
  isActive: boolean
}
```

#### MallDetailsPageData
```typescript
Mall & {
  floors: {
    id: string
    number: number
    name: string
  }[]
  categories: string[]
  mallHours: MallHour[]
  amenities: string[]
}
```

#### DashboardData
```typescript
{
  malls: { total: number, difference: number }
  shops: { total: number, difference: number }
  users: { total: number, difference: number }
  activeUsers: { total: number, percentage: number }
  pageViews: { total: number, percentage: number }
  recentMalls: Mall[]
  recentShops: Shop[]
  recentReports: Report[]
  recentUsers: User[]
}
```

#### Pagination Meta
```typescript
{
  page: number
  limit: number
  total: number
  hasNext: boolean
}
```

## ⚠️ Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "error": "Error type",
  "message": "Detailed error message"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request (Invalid input, validation errors)
- `401`: Unauthorized (Invalid or missing token)
- `403`: Forbidden (Insufficient permissions)
- `404`: Not Found (Resource doesn't exist)
- `500`: Internal Server Error

### Common Error Scenarios

#### Authentication Errors
```json
{
  "success": false,
  "error": "Unauthorized - No token provided",
  "message": "Authentication required"
}
```

#### Validation Errors
```json
{
  "success": false,
  "error": "Validation failed",
  "message": "Invalid input data"
}
```

#### Permission Errors
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Admin access required"
}
```

## 🚀 Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run seed         # Seed database with sample data
npm run delete       # Delete all data from database
```

## 📁 Project Structure

```
├── app/
│   ├── api/v1/          # API routes
│   │   ├── auth/        # Authentication endpoints
│   │   ├── malls/       # Mall endpoints
│   │   ├── shops/       # Shop endpoints
│   │   ├── floors/      # Floor endpoints
│   │   ├── users/       # User endpoints
│   │   ├── admin/       # Admin endpoints
│   │   ├── categories/  # Category endpoints
│   │   └── amenities/   # Amenity endpoints
│   └── ...
├── lib/
│   ├── services/        # Business logic
│   ├── middleware/      # Auth & validation middleware
│   ├── types/          # TypeScript types
│   ├── generated/      # Prisma & Zod generated types
│   └── utils/          # Utility functions
├── components/         # React components
├── prisma/            # Database schema & migrations
└── ...
```

## 🔧 Development Tips

### Authentication Testing
Use tools like Postman or curl to test authenticated endpoints:

```bash
# Login first to get cookies
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  -c cookies.txt

# Use cookies for authenticated requests
curl -X GET http://localhost:3000/api/v1/users/profile \
  -b cookies.txt
```

### Database Seeding
The project includes seeding scripts for development:

```bash
# Seed with 5 malls and related data
npm run seed

# Delete all data
npm run delete
```

### Environment Setup
Make sure to set up your environment variables properly:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/mallexplorer"

# JWT Secrets (use strong, unique values in production)
JWT_SECRET="your-super-secret-jwt-key-min-32-chars"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-min-32-chars"

# Optional: For development
NODE_ENV="development"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.
