'use client'
import { useEffect, useRef, useState } from 'react';

interface Shop {
    id: number;
    name: string;
    x: number;
    y: number;
    width: number;
    height: number;
    category: string;
}

interface ColorSet {
    fill: string;
    stroke: string;
    text: string;
}

const MallFloorPlan = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [shops, setShops] = useState<Shop[]>([]);
    const [selectedShop, setSelectedShop] = useState<Shop | null>(null);
    const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });

    // Enhanced dummy shop data with categories and colors
    const dummyShops: Shop[] = [

        {
            "id": 1748378835114,
            "name": "New Shop",
            "x": 0,
            "y": 0,
            "width": 142,
            "height": 179,
            "category": "food"
        },
        {
            "id": 1748378836458,
            "name": "New Shop",
            "x": 188,
            "y": 0,
            "width": 128,
            "height": 183,
            "category": "food"
        },
        {
            "id": 1748378837253,
            "name": "New Shop",
            "x": 474,
            "y": 0,
            "width": 98,
            "height": 155,
            "category": "health"
        },
        {
            "id": 1748378847298,
            "name": "New Shop",
            "x": 347,
            "y": 0,
            "width": 119,
            "height": 111,
            "category": "clothing"
        },
        {
            "id": 1748378853594,
            "name": "New Shop",
            "x": 597,
            "y": 0,
            "width": 146,
            "height": 145,
            "category": "beauty"
        },
        {
            "id": 1748378857642,
            "name": "New Shop",
            "x": 666,
            "y": 162,
            "width": 112,
            "height": 136,
            "category": "sports"
        },
        {
            "id": 1748378860074,
            "name": "New Shop",
            "x": 664,
            "y": 315,
            "width": 93,
            "height": 110,
            "category": "gifts"
        },
        {
            "id": 1748378862274,
            "name": "New Shop",
            "x": 681,
            "y": 457,
            "width": 109,
            "height": 121,
            "category": "sports"
        },
        {
            "id": 1748378864322,
            "name": "New Shop",
            "x": 470,
            "y": 387,
            "width": 131,
            "height": 141,
            "category": "health"
        },
        {
            "id": 1748378865610,
            "name": "New Shop",
            "x": 199,
            "y": 439,
            "width": 151,
            "height": 156,
            "category": "sports"
        },
        {
            "id": 1748378867354,
            "name": "New Shop",
            "x": 47,
            "y": 311,
            "width": 125,
            "height": 127,
            "category": "books"
        }
    ]

    // Color scheme for categories
    const categoryColors: Record<string, ColorSet> = {
        clothing: { fill: '#93c5fd', stroke: '#1d4ed8', text: '#1e3a8a' },
        electronics: { fill: '#c4b5fd', stroke: '#5b21b6', text: '#4c1d95' },
        books: { fill: '#fcd34d', stroke: '#92400e', text: '#78350f' },
        food: { fill: '#fca5a5', stroke: '#b91c1c', text: '#991b1b' },
        sports: { fill: '#86efac', stroke: '#047857', text: '#065f46' },
        beauty: { fill: '#f9a8d4', stroke: '#9d174d', text: '#831843' },
        gifts: { fill: '#f5d0fe', stroke: '#86198f', text: '#701a75' },
        health: { fill: '#bae6fd', stroke: '#0369a1', text: '#075985' },
        default: { fill: '#e5e7eb', stroke: '#6b7280', text: '#1f2937' }
    };

    // Initialize shops
    useEffect(() => {
        setShops(dummyShops);

        const updateCanvasSize = () => {
            const container = canvasRef.current?.parentElement;
            if (container) {
                // setCanvasSize({
                //     width: container.clientWidth,
                //     height: Math.min(container.clientWidth * 0.75, window.innerHeight * 0.8)
                // });
                setCanvasSize({
                    width: 800,
                    height: 600
                });
            }
        };

        updateCanvasSize();
        window.addEventListener('resize', updateCanvasSize);
        return () => window.removeEventListener('resize', updateCanvasSize);
    }, []);

    // Helper to wrap text in canvas
    const wrapText = (
        ctx: CanvasRenderingContext2D,
        text: string,
        x: number,
        y: number,
        maxWidth: number,
        lineHeight: number
    ) => {
        const words = text.split(' ');
        let line = '';
        let lines: string[] = [];

        for (let n = 0; n < words.length; n++) {
            const testLine = line + words[n] + ' ';
            const metrics = ctx.measureText(testLine);
            const testWidth = metrics.width;
            if (testWidth > maxWidth && n > 0) {
                lines.push(line.trim());
                line = words[n] + ' ';
            } else {
                line = testLine;
            }
        }
        lines.push(line.trim());

        for (let i = 0; i < lines.length; i++) {
            ctx.fillText(lines[i], x, y + (i - (lines.length - 1) / 2) * lineHeight);
        }
    };

    // Helper to draw color legend
    const drawLegend = (ctx: CanvasRenderingContext2D, x: number, y: number) => {
        const legendWidth = 120;
        const itemHeight = 20;

        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.strokeStyle = '#e5e7eb';
        ctx.lineWidth = 1;
        ctx.beginPath();
        // Using rounded rect polyfill since TS doesn't know about roundRect
        roundRect(ctx, x, y, legendWidth, Object.keys(categoryColors).length * itemHeight + 30, 8);
        ctx.fill();
        ctx.stroke();

        ctx.fillStyle = '#1f2937';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Shop Categories', x + legendWidth / 2, y + 18);

        ctx.font = '10px Arial';
        ctx.textAlign = 'left';

        let i = 0;
        for (const [category, colors] of Object.entries(categoryColors)) {
            if (category === 'default') continue;

            ctx.fillStyle = colors.fill;
            ctx.beginPath();
            roundRect(ctx, x + 10, y + 30 + i * itemHeight, 12, 12, 3);
            ctx.fill();

            ctx.strokeStyle = colors.stroke;
            ctx.lineWidth = 1;
            ctx.stroke();

            ctx.fillStyle = '#1f2937';
            ctx.fillText(
                category.charAt(0).toUpperCase() + category.slice(1),
                x + 28,
                y + 40 + i * itemHeight
            );
            i++;
        }
    };

    // Polyfill for roundRect since it's not in the TS lib yet
    const roundRect = (
        ctx: CanvasRenderingContext2D,
        x: number,
        y: number,
        width: number,
        height: number,
        radius: number
    ) => {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    };

    // Color manipulation helpers
    const lightenColor = (color: string, percent: number): string => {
        const num = parseInt(color.replace('#', ''), 16);
        const amt = Math.round(2.55 * percent);
        const R = Math.min(255, (num >> 16) + amt);
        const G = Math.min(255, (num >> 8 & 0x00FF) + amt);
        const B = Math.min(255, (num & 0x0000FF) + amt);
        return `#${(
            0x1000000 +
            (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
            (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
            (B < 255 ? (B < 1 ? 0 : B) : 255)
        ).toString(16).slice(1)}`;
    };

    const darkenColor = (color: string, percent: number): string => {
        const num = parseInt(color.replace('#', ''), 16);
        const amt = Math.round(2.55 * percent);
        const R = Math.max(0, (num >> 16) - amt);
        const G = Math.max(0, (num >> 8 & 0x00FF) - amt);
        const B = Math.max(0, (num & 0x0000FF) - amt);
        return `#${(
            0x1000000 +
            (R > 0 ? (R < 255 ? R : 255) : 0) * 0x10000 +
            (G > 0 ? (G < 255 ? G : 255) : 0) * 0x100 +
            (B > 0 ? (B < 255 ? B : 255) : 0)
        ).toString(16).slice(1)}`;
    };

    // Draw the mall floor plan
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Clear canvas with light background
        ctx.fillStyle = '#f9fafb';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw paths/aisles
        ctx.strokeStyle = '#e5e7eb';
        ctx.lineWidth = 3;

        // Main horizontal aisle
        ctx.beginPath();
        ctx.moveTo(50, 250);
        ctx.lineTo(canvas.width - 50, 250);
        ctx.stroke();

        // Main vertical aisle
        ctx.beginPath();
        ctx.moveTo(400, 50);
        ctx.lineTo(400, canvas.height - 50);
        ctx.stroke();

        // Draw each shop
        shops.forEach(shop => {
            const colors = categoryColors[shop.category] || categoryColors.default;
            const isSelected = shop.id === selectedShop?.id;

            // Shop rectangle
            ctx.fillStyle = isSelected ? lightenColor(colors.fill, 20) : colors.fill;
            ctx.strokeStyle = isSelected ? darkenColor(colors.stroke, 20) : colors.stroke;
            ctx.lineWidth = isSelected ? 3 : 2;

            ctx.beginPath();
            roundRect(ctx, shop.x, shop.y, shop.width, shop.height, 6);
            ctx.fill();
            ctx.stroke();

            // Shop name - center and wrap text if needed
            ctx.fillStyle = colors.text;
            ctx.font = 'bold 11px Arial';
            ctx.textAlign = 'center';

            wrapText(
                ctx,
                shop.name,
                shop.x + shop.width / 2,
                shop.y + shop.height / 2,
                shop.width - 20,
                16
            );

            // Shop ID in corner
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`#${shop.id}`, shop.x + 8, shop.y + 18);
        });

        // Draw entrance with better visual
        ctx.fillStyle = '#10b981';
        ctx.beginPath();
        ctx.arc(50, 250, 15, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#047857';
        ctx.lineWidth = 2;
        ctx.stroke();

        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('ENTRANCE', 50, 250 + 30);

        // Add legend if space allows
        if (canvas.width > 600) {
            drawLegend(ctx, canvas.width - 150, 20);
        }
    }, [shops, selectedShop, canvasSize]);

    // Handle shop selection
    const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const clickedShop = shops.find(shop =>
            x >= shop.x && x <= shop.x + shop.width &&
            y >= shop.y && y <= shop.y + shop.height
        );

        setSelectedShop(clickedShop || null);
    };

    return (
        <div className="flex flex-col items-center p-4 sm:p-6 max-w-7xl mx-auto">
            <div className="text-center mb-6">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">Mall Floor Plan</h1>
                <p className="text-gray-600 max-w-2xl">
                    Click on any shop to view details. Shops are color-coded by category for easy navigation.
                </p>
            </div>

            <div className="w-full max-w-6xl border border-gray-200 rounded-xl shadow-sm overflow-hidden mb-6 bg-white">
                <canvas
                    ref={canvasRef}
                    width={canvasSize.width}
                    height={canvasSize.height}
                    onClick={handleCanvasClick}
                    className="w-full bg-gray-50 cursor-pointer"
                    aria-label="Interactive mall floor plan with shops"
                />
            </div>

            {selectedShop ? (
                <div
                    className="w-full max-w-md p-5 rounded-xl shadow-sm border-l-4 bg-white transition-all duration-200"
                    style={{ borderLeftColor: categoryColors[selectedShop.category]?.stroke || categoryColors.default.stroke }}
                >
                    <h3 className="text-lg font-bold text-gray-800 mb-3">{selectedShop.name}</h3>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <p className="text-gray-500 font-medium">Shop ID</p>
                            <p className="text-gray-800">#{selectedShop.id}</p>
                        </div>
                        <div>
                            <p className="text-gray-500 font-medium">Category</p>
                            <p className="capitalize text-gray-800">{selectedShop.category}</p>
                        </div>
                        <div>
                            <p className="text-gray-500 font-medium">Location</p>
                            <p className="text-gray-800">Floor 1, Section {Math.ceil(selectedShop.x / 200)}</p>
                        </div>
                        <div>
                            <p className="text-gray-500 font-medium">Coordinates</p>
                            <p className="text-gray-800">({selectedShop.x}, {selectedShop.y})</p>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="w-full max-w-md p-5 rounded-xl bg-gray-50 text-center">
                    <p className="text-gray-600">Click on a shop to see details</p>
                </div>
            )}
        </div>
    );
};

export default MallFloorPlan;