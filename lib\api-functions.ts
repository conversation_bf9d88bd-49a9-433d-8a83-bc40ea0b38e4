import axios from "axios"
import { ApiResponse } from "./types/api-response"
import { Mall, User } from "./generated/prisma"
import { FeaturedMallData, GetMallsOptions, ShopCardData } from "./types/mall"
import { z } from "zod"
import { UserUpdateInputSchema } from "./generated/zod"
import { api } from "./api/client"

export async function getAmenities() {
  const data = (await api.get("/amenities")).data as ApiResponse<string[]>
  return data
}

export async function getCategories(): Promise<string[]> {
  const data = (await api.get("/categories")).data as ApiResponse<string[]>
  return data.data ?? [];
}

export async function getMalls({
  page = 1,
  limit = 12,
  activeOnly = true,
  searchTerm = "",
  category = "All",
  city = "All",
  state = "All",
  country = "All",
  amenities = [],
  sortBy = "name",
}: GetMallsOptions): Promise<Mall[]> {
  const data = await api.get("/malls", {
    params: {
      page,
      limit,
      activeOnly,
      searchTerm,
      category,
      city,
      state,
      country,
      amenities,
      sortBy,
    },
  }) as ApiResponse<Mall[]>
  return data.data ?? []
}

export async function getFeaturedMalls(): Promise<FeaturedMallData[]> {
  const data = await api.get("/malls/featured") as ApiResponse<FeaturedMallData[]>
  return data.data ?? []
}

export async function getMall(slug: string): Promise<Mall | null> {
  const data = await api.get(`/malls/${slug}`) as ApiResponse<Mall>
  return data.data ?? null
}

export async function getMallShops(mallId: string, options?: {
  page?: number;
  limit?: number;
  searchTerm?: string;
  category?: string;
  floor?: string;
}): Promise<ShopCardData[]> {
  const data = await api.get(`/malls/${mallId}/shops`, {
    params: options,
  }) as ApiResponse<ShopCardData[]>
  return data.data ?? []
}

export async function getShops(options: {
  page: number;
  limit: number;
  mallId?: string | null;
  floor?: string | null;
}): Promise<ShopCardData[]> {
  const data = await api.get("/shops", {
    params: options,
  }) as ApiResponse<ShopCardData[]>
  return data.data ?? []
}

export async function getShopsByCategoryId(categoryId: string): Promise<ShopCardData[]> {
  const data = await api.get("/shops/category", {
    params: { id: categoryId },
  }) as ApiResponse<ShopCardData[]>
  return data.data ?? []
}

export async function getShopsByFloorId(floorId: string): Promise<ShopCardData[]> {
  const data = await api.get("/shops/floor", {
    params: { id: floorId },
  }) as ApiResponse<ShopCardData[]>
  return data.data ?? []
}

export async function getShopById(id: string): Promise<ShopCardData | null> {
  const data = await api.get(`/shops/${id}`) as ApiResponse<ShopCardData>
  return data.data ?? null
}

// User functions
export async function getUserProfile(): Promise<User | null> {
  const data = await api.get("/users/profile") as ApiResponse<User>
  return data.data ?? null
}

export async function updateUserProfile(updates: Partial<z.infer<typeof UserUpdateInputSchema>>): Promise<User | null> {
  const data = await api.put("/users/profile", updates) as ApiResponse<User>
  return data.data ?? null
}

