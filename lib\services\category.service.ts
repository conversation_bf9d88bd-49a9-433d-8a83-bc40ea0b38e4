import { ApiError } from '../api-error';
import prisma from '../prisma';
import { ApiResponse } from '../types/api-response';

export const getCategories = async (): Promise<ApiResponse<string[]>> => {
    try {
        const floors = await prisma.floor.findMany({
            select: {
                categories: true,
            },
        });

        // Flatten and deduplicate categories from all floors
        const categories = Array.from(
            new Set(floors.flatMap((floor) => floor.categories))
        );

        return {
            success: true,
            data: categories,
        };
    } catch (error: any) {
        throw new ApiError(400, error.message);
    }
};