import { NextRequest, NextResponse } from 'next/server'
import { ZodSchema } from 'zod'
import { errorHandler } from '@/lib/api-error'
import { validate } from '@/lib/middleware/validation'
import { ApiResponse } from '@/lib/types/api-response'
import { authMiddleware } from '../middleware/auth'

// Generic route handler type
type RouteHandler<T = any> = (
  request: NextRequest,
  context?: { params: any }
) => Promise<ApiResponse<T>>

// Generic GET handler
export function createGetHandler<T>(
  handler: (request: NextRequest, context?: { params: any }) => Promise<ApiResponse<T>>
) {
  return async (request: NextRequest, context?: { params: any }) => {
    try {
      const result = await handler(request, context)
      return NextResponse.json(result)
    } catch (error) {
      return errorHandler(error)
    }
  }
}

// Generic POST handler with validation
export function createPostHandler<T, V = any>(
  handler: (data: V, request: NextRequest, context?: { params: any }) => Promise<ApiResponse<T>>,
  schema?: ZodSchema<V>
) {
  return async (request: NextRequest, context?: { params: any }) => {
    try {
      const body = await request.json()
      
      let validatedData = body
      if (schema) {
        validatedData = await validate(schema, body)
      }
      
      const result = await handler(validatedData, request, context)
      return NextResponse.json(result, { status: 201 })
    } catch (error) {
      return errorHandler(error)
    }
  }
}

// Generic PUT handler with validation
export function createPutHandler<T, V = any>(
  handler: (data: V, request: NextRequest, context?: { params: any }) => Promise<ApiResponse<T>>,
  schema?: ZodSchema<V>
) {
  return async (request: NextRequest, context?: { params: any }) => {
    try {
      const body = await request.json()
      
      let validatedData = body
      if (schema) {
        validatedData = await validate(schema, body)
      }
      
      const result = await handler(validatedData, request, context)
      return NextResponse.json(result)
    } catch (error) {
      return errorHandler(error)
    }
  }
}

// Generic DELETE handler
export function createDeleteHandler<T>(
  handler: (request: NextRequest, context?: { params: any }) => Promise<ApiResponse<T>>
) {
  return async (request: NextRequest, context?: { params: any }) => {
    try {
      const result = await handler(request, context)
      return NextResponse.json(result)
    } catch (error) {
      return errorHandler(error)
    }
  }
}

// Helper to extract query parameters with types
export function getQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  return {
    getString: (key: string, defaultValue?: string) => 
      searchParams.get(key) || defaultValue!,
    
    getNumber: (key: string, defaultValue?: number) => {
      const value = searchParams.get(key)
      return value ? parseInt(value, 10) : defaultValue!
    },
    
    getBoolean: (key: string, defaultValue?: boolean) => {
      const value = searchParams.get(key)
      return value ? value === 'true' : defaultValue!
    },
    
    getArray: (key: string) => 
      searchParams.getAll(key),
    
    getAll: () => Object.fromEntries(searchParams.entries())
  }
}

// Helper to extract path parameters
export async function getPathParams<T = Record<string, string>>(
  context?: { params: Promise<T> | T }
): Promise<T> {
  if (!context?.params) {
    return {} as T
  }
  
  // Handle both Promise and direct params
  if (context.params instanceof Promise) {
    return await context.params
  }
  
  return context.params
}

// Pagination helper
export function getPaginationParams(request: NextRequest) {
  const query = getQueryParams(request)
  
  const page = query.getNumber('page', 1)
  const limit = Math.min(query.getNumber('limit', 10), 100) // Max 100 items per page
  const skip = (page - 1) * limit
  
  return { page, limit, skip }
}

// Search and filter helper
export function getFilterParams(request: NextRequest) {
  const query = getQueryParams(request)
  
  return {
    searchTerm: query.getString('searchTerm'),
    category: query.getString('category'),
    sortBy: query.getString('sortBy'),
    order: query.getString('order', 'asc') as 'asc' | 'desc',
    ...query.getAll()
  }
}

// Response helpers
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  meta?: any
): ApiResponse<T> {
  return {
    success: true,
    data,
    message,
    meta
  }
}

export function createErrorResponse(
  error: string,
  message?: string
): ApiResponse<never> {
  return {
    success: false,
    error,
    message
  }
}

// Middleware composition helper
export function withMiddleware<T>(
  handler: RouteHandler<T>,
  ...middlewares: Array<(request: NextRequest, context?: any) => Promise<void> | void>
) {
  return async (request: NextRequest, context?: any) => {
    // Run middlewares in sequence
    for (const middleware of middlewares) {
      await middleware(request, context)
    }
    
    // Run the actual handler
    return handler(request, context)
  }
}
