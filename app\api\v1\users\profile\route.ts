
import { NextRequest, NextResponse } from 'next/server';
import { getUserProfile, updateUserProfile } from '@/lib/services/auth.service';
import { errorHandler } from '@/lib/api-error';
import { authMiddleware } from '@/lib/middleware/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authMiddleware(request);
    const result = await getUserProfile(user.userId);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await authMiddleware(request);
    const body = await request.json();
    const result = await updateUserProfile(user.userId, body);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}
