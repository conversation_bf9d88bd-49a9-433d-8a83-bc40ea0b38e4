import { NextRequest, NextResponse } from 'next/server';
import { LoginInput, loginUser } from '@/lib/services/auth.service';
import { validate } from '@/lib/middleware/validation';
import { errorHandler } from '@/lib/api-error';
import { UserWhereUniqueInputSchema } from '@/lib/generated/zod';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
    try {
        const body = (await request.json()) as LoginInput;
        const validatedData = await validate(UserWhereUniqueInputSchema, body);
        const result = (await loginUser(validatedData));
        (await cookies()).set({
            name: 'token',
            value: result?.data?.accessToken ?? '',
            path: '/',
            maxAge: 15 * 60 * 1000, // 15 minutes
        });
        (await cookies()).set({
            name: 'refreshToken',
            value: result?.data?.refreshToken ?? '',
            path: '/',
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });
        return NextResponse.json(result, { status: 200 })
    } catch (error) {
        return errorHandler(error);
    }
}