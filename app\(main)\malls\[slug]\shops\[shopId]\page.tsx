import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, MapPin, Clock, Phone, Mail, Globe } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { getShopById } from "@/lib/services/shop.service"
import { ShopDetailsPageData } from "@/lib/types/shop"

export default async function ShopDetailPage({
  params,
}: {
  params: Promise<{ slug: string; shopId: string }>
}) {
  const { slug, shopId } = await params;
  const shop = (await getShopById(shopId)).data as ShopDetailsPageData;

  // Find similar stores in the same mall and category
  const similarStores = [] as any;

  if (!shop) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Shop Not Found</h1>
          <Button asChild>
            <Link href={`/malls/${slug}`}>Back to Mall</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container px-4 md:px-6">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
          <Link href="/malls" className="hover:text-foreground">
            Malls
          </Link>
          <span>/</span>
          <Link href={`/malls/${slug}`} className="hover:text-foreground">
            {slug.split("-").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
          </Link>
          <span>/</span>
          <span className="text-foreground">{shop.name}</span>
        </nav>

        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href={`/malls/${slug}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to {
              slug.split("-").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")
            }
          </Link>
        </Button>

        {/* Shop Header */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="space-y-4">
            <Image
              src={shop.image || "/placeholder.svg"}
              alt={shop.name}
              width={600}
              height={400}
              className="w-full h-64 lg:h-[21rem] object-cover rounded-lg"
            />
            {/* Gallery */}
            <div className="grid grid-cols-4 gap-2">
              {shop.images.map((image, _) => (
                <Image
                  key={_}
                  src={image}
                  alt={`${shop.name} gallery ${_}`}
                  width={150}
                  height={120}
                  className="w-full h-20 lg:h-24 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                />
              ))}
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline">Floor {shop.floor}</Badge>
                <Badge variant="secondary">{shop.category}</Badge>
              </div>
              <h1 className="text-4xl font-bold mb-4">{shop.name}</h1>
              <p className="text-muted-foreground text-lg">{shop.description}</p>
            </div>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Hours: {shop.hours[0].open} - {shop.hours[0].close}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{shop.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{shop.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{shop.website ?? 'N/A'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Floor {shop.floor}, {shop.wing ?? 'N/A'}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button className="flex-1">
                <Phone className="mr-2 h-4 w-4" />
                Call Store
              </Button>
              <Button variant="outline" className="flex-1">
                <MapPin className="mr-2 h-4 w-4" />
                Get Directions
              </Button>
            </div>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Detailed Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            {/* About */}
            <section>
              <h2 className="text-2xl font-bold mb-4">About {shop.name}</h2>
              <p className="text-muted-foreground leading-relaxed">
                {shop.description}
              </p>
            </section>

            {/* Products */}
            <section>
              <h2 className="text-2xl font-bold mb-4">Products</h2>
              <div className="flex flex-wrap gap-2">
                {["Various " + shop.category.toLowerCase() + " products"].map((product, _) => (
                  <Badge key={_} variant="outline" className="text-sm py-1 px-3">
                    {product}
                  </Badge>
                ))}
              </div>
            </section>

            {/* Services */}
            <section>
              <h2 className="text-2xl font-bold mb-4">Services</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {["In-store shopping", "Customer support"].map((service, _) => (
                  <div key={_} className="flex items-center gap-2 p-3 border rounded-lg">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span>{service}</span>
                  </div>
                ))}
              </div>
            </section>
          </div>

          <div className="space-y-6">
            {/* Features */}
            {shop.specialFeatures && shop.specialFeatures.length > 0 && <Card>
              <CardHeader>
                <CardTitle>Store Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {
                    shop.specialFeatures.map((feature, _) => (
                      <div key={_} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))
                  }
                </div>
              </CardContent>
            </Card>}

            {/* Location in Mall */}
            <Card>
              <CardHeader>
                <CardTitle>Location in Mall</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Floor {shop.floor}</div>
                  {/* <div className="text-sm text-muted-foreground">{malName}</div> */}
                  <div className="w-full h-32 bg-muted rounded-lg flex items-center justify-center">
                    <span className="text-muted-foreground">Interactive Floor Map</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}