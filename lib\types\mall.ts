import { z } from "zod";
import { MallCreateInputSchema, MallUpdateInputSchema } from "../generated/zod";
import { Mall, MallHour, Shop, ShopHour } from "../generated/prisma";

export interface FeaturedMallData {
  id: string;
  name: string;
  city: string | null;
  state: string | null;
  address: string | null;
  country: string | null;
  shortDescription: string | null;
  image: string | undefined;
  averageFootFall: number | null;
  totalShops: number | null;
  totalFloors: number;
  categories: string[];
  slug: string;
  logo: string | null;
  rating: number | null;
  isActive: boolean;
};

export type MallUpdateInput = z.infer<typeof MallUpdateInputSchema>;
export type MallCreateInput = z.infer<typeof MallCreateInputSchema>;

export type GetMallsOptions = {
  page: number;
  limit: number;
  activeOnly?: boolean;
  searchTerm?: string;
  category?: string;
  city?: string;
  state?: string;
  country?: string;
  amenities?: string[];
  sortBy?: string;
};

export type MallDetailsPageData = Mall & {
  floors: {
    id: string;
    number: number;
    name: string;
  }[];
  categories: string[]
  mallHours: MallHour[];
  amenities: string[];
};

export type ShopCardData = Shop & {
  category: string;
  hours: ShopHour[];
};
