'use client'
import Image from 'next/image'
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import Link from 'next/link'
import { Badge } from './ui/badge'
import { MapPin, Star, Store } from 'lucide-react'
import { motion } from 'motion/react'
import { isMallOpen } from '@/lib/functions'
import dynamic from 'next/dynamic'
import { Mall } from '@/lib/generated/prisma'
const MapModal = dynamic(() => import("@/components/map-modal"), { ssr: false })

export default function MallCard({ mall }: { mall: Mall }) {
    const [showMap, setShowMap] = React.useState(false);

    return (
        <>

            <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                viewport={{ once: true, amount: 0.5 }}
            >

                <Card key={mall.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 transform flex flex-col h-full">
                    <div className="relative">
                        <Image
                            src={mall.images[0] || "/placeholder.svg"}
                            alt={mall.name}
                            width={400}
                            height={250}
                            className="w-full h-48 object-cover"
                        />
                        <div className="absolute top-4 right-4 flex gap-2">
                            <Badge variant="secondary" className="bg-background/95 backdrop-blur-sm shadow-sm">
                                {mall.averageFootFall} People
                            </Badge>
                            {/* {mall.rating && <Badge variant="secondary" className="bg-background/95 backdrop-blur-sm shadow-sm flex items-center gap-1">
                            <Star className="h-3 w-3 fill-current" />
                            {mall.rating}
                        </Badge>} */}
                        </div>
                        {/* {isOpen && <div className="absolute top-4 left-4 flex gap-2">
                            <Badge variant="secondary" className="bg-background/95 backdrop-blur-sm shadow-sm bg-green-600">
                                Open
                            </Badge>
                        </div>} */}
                    </div>

                    <div className="flex flex-col flex-1">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-xl font-semibold line-clamp-2">{mall.name}</CardTitle>
                            <CardDescription className="flex items-center gap-1 text-sm">
                                <MapPin className="h-4 w-4" />
                                {mall.address}
                            </CardDescription>
                        </CardHeader>

                        <CardContent className="flex-1 flex flex-col justify-between space-y-4 pt-0">
                            <div className="space-y-4">
                                <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                                    {mall.description}
                                </p>

                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                        <Store className="h-4 w-4" />
                                        <span className="font-medium">{mall.totalShops}</span> Stores
                                    </div>
                                </div>

                                {/* <div className="flex flex-wrap gap-2">
                                    {mall.a.map((category: string) => (
                                        <Badge
                                            key={category}
                                            variant="outline"
                                            className="text-xs px-2 py-1"
                                        >
                                            {category}
                                        </Badge>
                                    ))}
                                </div> */}
                            </div>

                            <div className='flex gap-2 mt-auto'>
                                <Button
                                    asChild
                                    className="flex-1"
                                >
                                    <Link href={`/malls/${mall.id}`}>
                                        View Details
                                    </Link>
                                </Button>
                                <Button variant="outline" onClick={() => setShowMap(true)}>
                                    <MapPin />
                                </Button>
                            </div>
                        </CardContent>
                    </div>
                </Card>
            </motion.div>
            <MapModal open={showMap} onClose={() => setShowMap(false)} lat={23.66441058541677}
                lng={86.14765028258206}
                popup="Bokaro Mall" zoom={15} />
        </>
    )
}
