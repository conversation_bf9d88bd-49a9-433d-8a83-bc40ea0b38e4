import { NextResponse } from 'next/server';
import { getFeaturedMalls } from '@/lib/services/mall.service';
import { errorHandler } from '@/lib/api-error';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '3');
    const isActiveOnly = searchParams.get('activeOnly') === 'true';
    
    const result = await getFeaturedMalls({ page, limit, isActiveOnly });
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}