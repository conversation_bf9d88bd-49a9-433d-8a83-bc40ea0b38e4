import { ApiError } from '../api-error';
import prisma from '../prisma';
import { ApiResponse } from '../types/api-response';

export const getAmenities = async (): Promise<ApiResponse<string[]>> => {
  try {
    const malls = await prisma.mall.findMany({
      select: {
        amenities: true,
      },
    });

    // Flatten and deduplicate amenities from all malls
    const amenities = Array.from(
      new Set(malls.flatMap((mall) => mall.amenities))
    );

    return {
      success: true,
      data: amenities,
    };
  } catch (error: any) {
    throw new ApiError(400, error.message);
  }
};