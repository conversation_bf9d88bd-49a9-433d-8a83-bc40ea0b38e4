{"name": "mallsurf", "description": "Mall Explorer", "author": "Darkx-dev", "version": "0.1.5", "private": true, "scripts": {"dev": "next dev", "build": "npx prisma generate --no-engine && next build", "start": "next start", "lint": "next lint", "delete": "tsx prisma/seed.ts --delete-all", "seed": "tsx prisma/seed.ts --seed --mall 5"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@prisma/client": "6.9.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "gsap": "^3.13.0", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.454.0", "mongoose": "^8.15.1", "motion": "^12.14.0", "next": "15.2.4", "next-themes": "latest", "radash": "^12.1.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-leaflet": "^5.0.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "^1.5.0", "vaul": "^0.9.6", "zod": "^3.24.1", "zod-prisma-types": "^3.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.18", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/use-sync-external-store": "^1.5.0", "@types/yargs": "^17.0.33", "i": "^0.3.7", "postcss": "^8", "prisma": "^6.9.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5", "yargs": "^18.0.0"}}