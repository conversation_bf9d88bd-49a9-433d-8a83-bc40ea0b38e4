import { NextRequest } from 'next/server'
import {
  create<PERSON>etHand<PERSON>,
  createPostHand<PERSON>,
  getPaginationParams,
  getQueryParams,
  createSuccessResponse,
} from '@/lib/api/route-handler'
import { getMalls, createMall } from '@/lib/services/mall.service'
import { MallCreateInputSchema } from '@/lib/generated/zod'
import { GetMallsOptions } from '@/lib/types/mall'
import { authMiddleware } from '@/lib/middleware/auth'

// GET /api/v1/malls
export const GET = createGetHandler(async (request: NextRequest) => {
  const { page, limit } = getPaginationParams(request)
  const query = getQueryParams(request)

  const options: GetMallsOptions = {
    page: page ?? 1,
    limit,
    activeOnly: query.getBoolean('activeOnly'),
    searchTerm: query.getString('searchTerm'),
    category: query.getString('category'),
    city: query.getString('city'),
    state: query.getString('state'),
    country: query.getString('country'),
    amenities: query.getArray('amenities'),
    sortBy: query.getString('sortBy'),
  }

  const result = await getMalls(options)
  return createSuccessResponse(result.data, undefined, result.meta)
})

// POST /api/v1/malls
export const POST = createPostHandler(
  async (validatedData, request) => {
    const user = await authMiddleware(request)
    if (user.role !== 'admin') {
      throw new Error('Unauthorized')
    }
    const result = await createMall(validatedData)
    return createSuccessResponse(result.data, 'Mall created successfully')
  },
  MallCreateInputSchema
)
