import { PrismaClient, ParkingType, UserRole, ReportType } from '../lib/generated/prisma';
// import { faker } from '../node_modules/@faker-js/faker';
// import yargs from 'yargs';
// import { hideBin } from 'yargs/helpers';
// import { addHours, startOfDay } from 'date-fns';

const prisma = new PrismaClient();

// // Parse command-line arguments
// const argv = yargs(hideBin(process.argv))
//   .option('seed', {
//     type: 'boolean',
//     description: 'Run the seed script',
//     default: false,
//   })
//   .option('malls', {
//     type: 'number',
//     description: 'Number of malls to seed',
//     default: 5,
//   })
//   .parseSync();

// // Random number generator for variable counts
// const getRandomInt = (min: number, max: number) =>
//   Math.floor(Math.random() * (max - min + 1)) + min;

// // Seed malls
// async function seedMalls(count: number) {
//   const malls = [];
//   for (let i = 0; i < count; i++) {
//     const mall = await prisma.mall.create({
//       data: {
//         name: faker.company.name() + ' Mall',
//         slug: faker.helpers.slugify(faker.company.name() + '-' + i),
//         address: faker.location.streetAddress(),
//         city: faker.location.city(),
//         state: faker.location.state(),
//         country: faker.location.country(),
//         pinCode: faker.location.zipCode(),
//         lat: parseFloat(faker.location.latitude().toFixed(6)),
//         lng: parseFloat(faker.location.longitude().toFixed(6)),
//         phone: faker.phone.number(),
//         email: faker.internet.email(),
//         customerCare: faker.phone.number(),
//         description: faker.lorem.paragraph(),
//         shortDescription: faker.lorem.sentence(),
//         images: Array.from({ length: 3 }, () => faker.image.url()),
//         logo: faker.image.url(),
//         rating: parseFloat(faker.number.float({ min: 3, max: 5, fractionDigits: 1 }).toFixed(1)),
//         featured: faker.datatype.boolean(),
//         parkingAvailable: faker.datatype.boolean(),
//         parkingCapacity: faker.datatype.boolean() ? getRandomInt(100, 1000) : null,
//         parkingType: faker.helpers.arrayElement([ParkingType.free, ParkingType.paid, ParkingType.valet]),
//         parkingRates: faker.datatype.boolean() ? `$${getRandomInt(5, 20)}/hour` : null,
//         totalShops: 0, // Updated later
//         averageFootFall: getRandomInt(1000, 10000),
//         yearOpened: getRandomInt(2000, 2023),
//         company: faker.company.name(),
//         manager: faker.person.fullName(),
//         contactPerson: faker.person.fullName(),
//         isActive: true,
//         createdAt: faker.date.recent({ days: 30 }),
//       },
//     });

//     // Seed mall hours
//     await prisma.mallHour.createMany({
//       data: Array.from({ length: 7 }, (_, day) => ({
//         mallId: mall.id,
//         day,
//         open: '09:00',
//         close: '21:00',
//         isClosed: day === 0 ? faker.datatype.boolean() : false,
//       })),
//     });

//     // Seed amenities
//     await prisma.amenity.createMany({
//       data: ['WiFi', 'Restrooms', 'Food Court', 'Parking'].map(name => ({
//         mallId: mall.id,
//         name,
//         icon: faker.image.url(),
//         description: faker.lorem.sentence(),
//         locations: [faker.lorem.word()],
//         hours: '09:00-21:00',
//         createdAt: mall.createdAt,
//       })),
//     });

//     // Seed floors
//     const floorCount = getRandomInt(2, 5);
//     await prisma.floor.createMany({
//       data: Array.from({ length: floorCount }, (_, idx) => ({
//         mallId: mall.id,
//         number: idx,
//         name: `Floor ${idx}`,
//         mapImage: faker.image.url(),
//         amenities: ['WiFi', 'Restrooms'],
//         createdAt: mall.createdAt,
//       })),
//     });

//     // Seed shops
//     const shopCount = getRandomInt(5, 20);
//     const categoryIds = (await prisma.category.findMany({ where: { floorId: { in: floorIds } }, select: { id: true } })).map(c => c.id);
//     await prisma.shop.createMany({
//       data: Array.from({ length: shopCount }, () => ({
//         categoryId: faker.helpers.arrayElement(categoryIds),
//         mallId: mall.id,
//         name: faker.company.name() + ' Store',
//         description: faker.lorem.sentence(),
//         phone: faker.phone.number(),
//         images: [faker.image.url()],
//         email: faker.internet.email(),
//         website: faker.internet.url(),
//         floor: getRandomInt(0, floorCount - 1),
//         wing: faker.lorem.word(),
//         coordinates: `${getRandomInt(1, 100)},${getRandomInt(1, 100)}`,
//         socialMedia: { instagram: faker.internet.url(), facebook: faker.internet.url() },
//         image: faker.image.url(),
//         logo: faker.image.url(),
//         featured: faker.datatype.boolean(),
//         specialFeatures: ['Wheelchair Accessible'],
//         rating: parseFloat(faker.number.float({ min: 3, max: 5, fractionDigits: 1 }).toFixed(1)),
//         tags: [faker.commerce.productAdjective()],
//         openingDate: faker.date.past(),
//         isActive: true,
//         createdAt: mall.createdAt,
//       })),
//     });

//     // Fetch shop IDs for shop hours
//     const shopIds = (await prisma.shop.findMany({ where: { mallId: mall.id }, select: { id: true } })).map(s => s.id);

//     // Seed shop hours
//     await prisma.shopHour.createMany({
//       data: shopIds.flatMap(shopId =>
//         Array.from({ length: 7 }, (_, day) => ({
//           shopId,
//           day,
//           open: '10:00',
//           close: '20:00',
//           isClosed: day === 0 ? faker.datatype.boolean() : false,
//         }))
//       ),
//     });

//     // Update mall's totalShops
//     await prisma.mall.update({
//       where: { id: mall.id },
//       data: { totalShops: shopCount },
//     });

//     malls.push(mall);
//   }
//   return malls;
// }

// // Seed users
// async function seedUsers(count: number) {
//   const users = [];
//   for (let i = 0; i < count; i++) {
//     const user = await prisma.user.create({
//       data: {
//         name: faker.person.fullName(),
//         email: faker.internet.email(),
//         password: faker.internet.password(),
//         phone: faker.phone.number(),
//         avatar: faker.image.avatar(),
//         role: faker.helpers.arrayElement([UserRole.admin, UserRole.user, UserRole.manager]),
//         isActive: faker.datatype.boolean(0.8),
//         lastLoginAt: faker.date.recent({ days: 7 }),
//         preferences: { theme: 'light' },
//         createdAt: faker.date.recent({ days: 30 }),
//       },
//     });
//     users.push(user);
//   }
//   return users;
// }

// // Seed page views
// async function seedPageViews(count: number, userIds: string[]) {
//   for (let i = 0; i < count; i++) {
//     await prisma.pageView.create({
//       data: {
//         userId: faker.helpers.arrayElement([...userIds, null]),
//         page: faker.internet.url(),
//         title: faker.lorem.words(3),
//         referrer: faker.internet.url(),
//         userAgent: faker.internet.userAgent(),
//         ipAddress: faker.internet.ip(),
//         sessionId: faker.string.uuid(),
//         duration: getRandomInt(10, 300),
//         viewedAt: faker.date.recent({ days: 30 }),
//       },
//     });
//   }
// }

// // Seed reports
// async function seedReports(count: number, userIds: string[]) {
//   for (let i = 0; i < count; i++) {
//     const dateFrom = startOfDay(faker.date.recent({ days: 30 }));
//     await prisma.report.create({
//       data: {
//         title: `${faker.lorem.words(3)} Report`,
//         description: faker.lorem.sentence(),
//         type: faker.helpers.arrayElement([
//           ReportType.MALL_ANALYTICS,
//           ReportType.USER_ANALYTICS,
//           ReportType.SHOP_ANALYTICS,
//           ReportType.PAGE_VIEW_ANALYTICS,
//           ReportType.CUSTOM,
//         ]),
//         data: { metrics: faker.lorem.words(5) },
//         filters: { period: 'monthly' },
//         dateFrom,
//         dateTo: addHours(dateFrom, getRandomInt(24, 720)),
//         generatedBy: faker.helpers.arrayElement(userIds),
//         createdAt: faker.date.recent({ days: 30 }),
//       },
//     });
//   }
// }

// // Main seed function
// async function main() {
//   if (!argv.seed) {
//     console.log('Please run with --seed flag');
//     return;
//   }

//   console.log(`Seeding ${argv.malls} malls...`);
//   const malls = await seedMalls(argv.malls);
//   console.log(`Seeded ${malls.length} malls`);

//   console.log('Seeding 10 users...');
//   const users = await seedUsers(10);
//   console.log(`Seeded ${users.length} users`);

//   console.log('Seeding 50 page views...');
//   await seedPageViews(50, users.map(u => u.id));
//   console.log('Seeded 50 page views');

//   console.log('Seeding 10 reports...');
//   await seedReports(10, users.map(u => u.id));
//   console.log('Seeded 10 reports');
// }

// // Execute and handle errors
// main()
//   .catch(e => {
//     console.error('Seeding failed:', e);
//     process.exit(1);
//   })
//   .finally(async () => {
//     await prisma.$disconnect();
//   });

async function bro() {
    await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { role: UserRole.admin },
    })
}

bro()