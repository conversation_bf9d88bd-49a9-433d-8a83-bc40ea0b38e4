import { Store, Users, MapPin } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import AnimatedNumber from "@/components/animated-number"

const stats = [
  { end: 25, suffix: "+", label: "Shopping Malls", icon: Store },
  { end: 2500, suffix: "+", label: "Stores & Shops", icon: Users },
  { end: 50, suffix: "+", label: "Categories", icon: MapPin }
]

export function StatsSection() {
  return (
    <section className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-r from-muted/30 to-muted/50 dark:from-muted/20 dark:to-muted/30"></div>
      <div className="container px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <Card className="border-0 bg-background/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <CardContent className="pt-8 pb-6">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 rounded-full bg-primary/10 dark:bg-primary/5 group-hover:bg-primary/20 dark:group-hover:bg-primary/10 transition-colors duration-300">
                      <stat.icon className="w-6 h-6 text-primary" />
                    </div>
                  </div>
                  <AnimatedNumber 
                    start={0} 
                    end={stat.end} 
                    suffix={stat.suffix} 
                    className="text-4xl md:text-5xl font-bold bg-gradient-to-br from-primary to-primary/70 bg-clip-text text-transparent mb-2" 
                  />
                  <p className="text-muted-foreground font-medium">{stat.label}</p>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
