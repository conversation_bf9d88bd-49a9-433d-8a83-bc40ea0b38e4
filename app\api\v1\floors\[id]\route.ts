
import { NextRequest, NextResponse } from 'next/server';
import { errorHand<PERSON> } from '@/lib/api-error';
import { deleteFloor, getFloorById, updateFloor } from '@/lib/services/floor.service';
import { authMiddleware } from '@/lib/middleware/auth';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const result = await getFloorById(params.id);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await authMiddleware(request);
    if (user.role !== 'admin') {
      throw new Error('Unauthorized');
    }
    const body = await request.json();
    const result = await updateFloor(params.id, body);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await authMiddleware(request);
    if (user.role !== 'admin') {
      throw new Error('Unauthorized');
    }
    const result = await deleteFloor(params.id);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

