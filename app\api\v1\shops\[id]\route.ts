import { NextResponse } from 'next/server';
import { getShopById, updateShop, deleteShop } from '@/lib/services/shop.service';
import { validate } from '@/lib/middleware/validation';
import { errorHandler } from '@/lib/api-error';
import { ShopSchema } from '@/lib/generated/zod';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const result = await getShopById(params.id);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const validatedData = await validate(ShopSchema, body);
    const result = await updateShop(params.id, validatedData);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const result = await deleteShop(params.id);
    return NextResponse.json(result);
  } catch (error) {
    return errorHandler(error);
  }
}