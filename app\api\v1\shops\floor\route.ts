import { NextResponse } from 'next/server';
import { getShopsByFloorId } from '@/lib/services/shop.service';
import { ApiError, errorHandler } from '@/lib/api-error';

export async function GET(
    request: Request
) {
    try {
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');
        if (!id) {
            throw new ApiError(400, 'Floor ID is required');
        }
        const result = await getShopsByFloorId(id);
        return NextResponse.json(result);
    } catch (error) {
        return errorHandler(error);
    }
}
