import React from 'react'
import { But<PERSON> } from './ui/button'
import Link from 'next/link'
import { ArrowRight, MapPin, Store, Users, Star, Sparkles } from 'lucide-react'
import Image from 'next/image'
import { Badge } from './ui/badge'
import { FeaturedMallData } from '@/lib/types/mall'
import { unique } from 'radash'

const getLocationString = (mall: FeaturedMallData): string => {
    const parts = [mall.city, mall.state].filter(Boolean)
    return parts.join(", ") || "Location TBD"
}

const formatFootfall = (footfall: number | null): string => {
    if (!footfall) return "N/A"
    if (footfall >= 1000000) {
        return `${(footfall / 1000000).toFixed(1)}M`
    }
    if (footfall >= 1000) {
        return `${(footfall / 1000).toFixed(0)}K`
    }
    return footfall.toString()
}

export default function FeaturedMallCard({ mall }: { mall: FeaturedMallData }) {
    return (
        <div className="group relative h-full overflow-hidden rounded-3xl bg-gradient-to-br from-background via-background to-muted/30 border-0 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 transform-gpu">
            {/* Animated Border Gradient */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary/20 via-secondary/20 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
            
            {/* Main Card Border */}
            <div className="absolute inset-0.5 rounded-3xl bg-background dark:bg-background" />
            
            {/* Content Container */}
            <div className="relative z-10 h-full flex flex-col rounded-3xl bg-gradient-to-br from-background/95 via-background to-muted/20 backdrop-blur-sm">
                {/* Image Section */}
                <div className="relative overflow-hidden">
                    <div className="relative">
                        <Image
                            src={mall.image || "/placeholder.svg"}
                            alt={mall.name}
                            width={400}
                            height={280}
                            className="w-full h-64 object-cover transition-all duration-700 group-hover:scale-110 group-hover:rotate-1"
                        />
                        
                        {/* Dynamic Overlay */}
                        <div className="absolute bottom-[-4] inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-500" />
                        
                        {/* Sparkle Effect */}
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 delay-200">
                            <Sparkles className="h-5 w-5 text-yellow-400 animate-pulse" />
                        </div>
                    </div>

                    {/* Enhanced Floating Badges */}
                    <div className="absolute top-4 left-4 flex flex-col gap-2">
                        {mall.totalFloors && (
                            <Badge className="bg-primary/95 text-primary-foreground backdrop-blur-md shadow-lg border-0 font-semibold px-3 py-1">
                                {mall.totalFloors} Floors
                            </Badge>
                        )}
                        {mall.averageFootFall && mall.averageFootFall > 10000 && (
                            <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white backdrop-blur-md shadow-lg border-0 font-semibold px-3 py-1">
                                <Star className="h-3 w-3 mr-1" />
                                Popular
                            </Badge>
                        )}
                    </div>

                    {/* Enhanced Icon Badge */}
                    <div className="absolute top-4 right-4 transform group-hover:scale-110 transition-transform duration-300">
                        <div className="bg-background/95 dark:bg-background/90 backdrop-blur-md rounded-full p-3 shadow-lg border border-border/50">
                            <Store className="h-5 w-5 text-primary" />
                        </div>
                    </div>

                    {/* Enhanced Bottom Overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/90 to-transparent">
                        <div className="transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                            <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-primary-foreground transition-colors">
                                {mall.name}
                            </h3>
                            <div className="flex items-center gap-2 text-white/90 text-sm mb-2">
                                <div className="flex items-center gap-1">
                                    <MapPin className="h-4 w-4" />
                                    <span className="font-medium">{getLocationString(mall)}</span>
                                </div>
                            </div>
                            
                            {/* Quick Stats */}
                            <div className="flex items-center gap-4 text-xs text-white/80 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-100">
                                <div className="flex items-center gap-1">
                                    <Store className="h-3 w-3" />
                                    <span>{mall.totalShops || 0} stores</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Users className="h-3 w-3" />
                                    <span>{formatFootfall(mall.averageFootFall)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Content Section */}
                <div className="p-6 space-y-5 flex-grow">
                    {/* Description with better typography */}
                    <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2 group-hover:text-foreground/80 transition-colors">
                        {mall.shortDescription || "Discover amazing shopping experiences at this premier destination with diverse retail options and entertainment."}
                    </p>

                    {/* Enhanced Stats Grid */}
                    <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center gap-2 p-3 rounded-2xl bg-muted/30 dark:bg-muted/20 group-hover:bg-primary/5 dark:group-hover:bg-primary/5 transition-colors">
                            <div className="p-2 rounded-full bg-primary/10 dark:bg-primary/10">
                                <Store className="h-4 w-4 text-primary" />
                            </div>
                            <div>
                                <p className="text-xs text-muted-foreground">Stores</p>
                                <p className="font-bold text-sm">{mall.totalShops || 0}</p>
                            </div>
                        </div>
                        
                        <div className="flex items-center gap-2 p-3 rounded-2xl bg-muted/30 dark:bg-muted/20 group-hover:bg-secondary/5 dark:group-hover:bg-secondary/5 transition-colors">
                            <div className="p-2 rounded-full bg-secondary/10 dark:bg-secondary/10">
                                <Users className="h-4 w-4 text-secondary" />
                            </div>
                            <div>
                                <p className="text-xs text-muted-foreground">Visitors</p>
                                <p className="font-bold text-sm">{formatFootfall(mall.averageFootFall)}</p>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Categories with better design */}
                    <div className="space-y-2">
                        <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Categories</p>
                        <div className="flex flex-wrap gap-2">
                            {unique(mall.categories).slice(0, 3).map((category, index) => (
                                <span
                                    key={category}
                                    className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-primary/10 to-primary/5 text-primary border border-primary/20 hover:from-primary/20 hover:to-primary/10 transition-all duration-200"
                                    style={{
                                        animationDelay: `${index * 100}ms`
                                    }}
                                >
                                    {category}
                                </span>
                            ))}
                            {unique(mall.categories).length > 3 && (
                                <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-muted/50 dark:bg-muted/30 text-muted-foreground border border-muted-foreground/20 hover:bg-muted/70 dark:hover:bg-muted/50 transition-colors">
                                    +{mall.categories.length - 3} more
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                {/* Enhanced Action Button - Pinned to Bottom */}
                <div className="p-6 pt-0">
                    <Button 
                        asChild 
                        className="w-full h-12 rounded-2xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 group/btn font-semibold"
                    >
                        <Link href={`/malls/${mall.slug}`}>
                            <span>Explore Mall</span>
                            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                        </Link>
                    </Button>
                </div>

                {/* Hover Glow Effect */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
            </div>
        </div>
    )
}