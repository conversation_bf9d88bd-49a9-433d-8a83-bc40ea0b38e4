"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  LayoutDashboard,
  Building2,
  Store,
  Users,
  Settings,
  BarChart3,
  MapPin,
  Plus,
  User2Icon,
  ChevronDown,
  Home,
  LogOut,
  HelpCircle,
} from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Separator } from "@/components/ui/separator"
import { useAuthStore, useUserStore } from "@/hooks"
import { api } from "@/lib"

const navigation = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/admin",
    description: "Overview & analytics"
  },
  {
    title: "Profile",
    icon: User2Icon,
    href: "/admin/profile",
    description: "Manage your account"
  },
  {
    title: "Malls",
    icon: Building2,
    href: "/admin/malls",
    badge: "25",
    badgeVariant: "default" as const,
    description: "Manage shopping centers",
    submenu: [
      { title: "All Malls", href: "/admin/malls", icon: Building2 },
      { title: "Add New Mall", href: "/admin/malls/new", icon: Plus },
    ],
  },
  {
    title: "Shops",
    icon: Store,
    href: "/admin/shops",
    badge: "2.5K+",
    badgeVariant: "secondary" as const,
    description: "Manage retail stores",
    submenu: [
      { title: "All Shops", href: "/admin/shops", icon: Store },
      { title: "Add New Shop", href: "/admin/shops/new", icon: Plus },
    ],
  },
  {
    title: "Users",
    icon: Users,
    href: "/admin/users",
    badge: "1.2K",
    badgeVariant: "outline" as const,
    description: "User management"
  },
]

const quickActions = [
  {
    title: "Add Mall",
    href: "/admin/malls/new",
    icon: Building2,
    color: "bg-blue-500 hover:bg-blue-600"
  },
  {
    title: "Add Shop",
    href: "/admin/shops/new",
    icon: Store,
    color: "bg-green-500 hover:bg-green-600"
  },
  {
    title: "View Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
    color: "bg-purple-500 hover:bg-purple-600"
  }
]

export function AdminSidebar() {
  const pathname = usePathname()

  const handleLogout = async () => {
    useUserStore.getState().logout()
    useAuthStore.getState().logout()
    await api.get('/auth/logout')
    alert(`Logout successful`)
    window.location.href = '/'
  }

  return (
    <Sidebar variant="sidebar" className="m-0">
      <SidebarHeader className="border-b border-border/40 bg-gradient-to-r from-primary/5 to-primary/10">
        <div className="flex items-center gap-3 p-1">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Building2 className="h-4 w-4" />
          </div>
          <div className="flex flex-col">
            <span className="font-bold text-lg">Admin Panel</span>
            <span className="text-xs text-muted-foreground">Mall Management System</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="p-0 w-full overflow-x-hidden">
        <SidebarGroup >
          <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground/80 uppercase tracking-wider px-2 py-2">
            Main Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {navigation.map((item) => (
                <SidebarMenuItem key={item.title}>
                  {item.submenu ? (
                    <Collapsible defaultOpen={pathname.startsWith(item.href)}>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          isActive={pathname.startsWith(item.href)}
                          className="w-full justify-between hover:bg-accent/50 transition-colors group rounded-lg p-2 h-fit"
                        >
                          <div className="flex items-center gap-3">
                            <div className={`p-1.5 rounded-md ${pathname.startsWith(item.href) ? 'bg-primary text-primary-foreground' : 'bg-muted group-hover:bg-accent'} transition-colors`}>
                              <item.icon className="h-4 w-4" />
                            </div>
                            <div className="flex flex-col items-start h-fit">
                              <span className="font-medium">{item.title}</span>
                              {/* <span className="text-xs text-muted-foreground">{item.description}</span> */}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {item.badge && (
                              <Badge variant={item.badgeVariant || "secondary"} className="text-xs">
                                {item.badge}
                              </Badge>
                            )}
                            <ChevronDown className="h-4 w-4 transition-transform group-data-[state=open]:rotate-180" />
                          </div>
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="ml-6 mt-1">
                        <SidebarMenuSub className="space-y-1">
                          {item.submenu.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton
                                asChild
                                isActive={pathname === subItem.href}
                                className="hover:bg-accent/50 transition-colors rounded-md"
                              >
                                <Link href={subItem.href} className="flex items-center gap-2 p-2">
                                  <subItem.icon className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-sm">{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </Collapsible>
                  ) : (
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.href}
                      className="hover:bg-accent/50 transition-colors h-fit group rounded-lg p-2"
                    >
                      <Link href={item.href} className="flex items-center gap-3">
                        <div className={`p-1.5 rounded-md ${pathname === item.href ? 'bg-primary text-primary-foreground' : 'bg-muted group-hover:bg-accent'} transition-colors`}>
                          <item.icon className="h-4 w-4" />
                        </div>
                        <div className="flex flex-col items-start flex-1">
                          <span className="font-medium">{item.title}</span>
                          {/* <span className="text-xs text-muted-foreground">{item.description}</span> */}
                        </div>
                        {/* {item.badge && (
                          <Badge variant={item.badgeVariant || "secondary"} className="text-xs">
                            {item.badge}
                          </Badge>
                        )} */}
                      </Link>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4 mx-2" />

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground/80 uppercase tracking-wider px-2 py-2">
            System
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="hover:bg-accent/50 transition-colors rounded-lg">
                  <Link href="/admin/settings" className="flex items-center gap-3 p-2">
                    <Settings className="h-4 w-4" />
                    <span>Settings</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="hover:bg-accent/50 transition-colors rounded-lg">
                  <Link href="/admin/help" className="flex items-center gap-3 p-2">
                    <HelpCircle className="h-4 w-4" />
                    <span>Help & Support</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-border/40 bg-gradient-to-r from-muted/20 to-muted/40">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="hover:bg-accent/50 transition-colors rounded-lg mb-2">
              <Link href="/" className="flex items-center gap-3 p-2">
                <Home className="h-4 w-4" />
                <span>View Public Site</span>
                <MapPin className="h-3 w-3 ml-auto text-muted-foreground" />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <div className="flex items-center gap-3 px-2 py-3 bg-background/50 rounded-lg border border-border/40">
            <Avatar className="h-8 w-8">
              <AvatarImage src="/admin-avatar.jpg" alt="Admin" />
              <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                AD
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col flex-1 min-w-0">
              <span className="text-sm font-medium truncate">Admin User</span>
              <span className="text-xs text-muted-foreground truncate"><EMAIL></span>
            </div>
            <Button variant="ghost" onClick={handleLogout} size="sm" className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}