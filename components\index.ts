// Common components
export { LoadingSpinner, LoadingPage, LoadingCard } from './common/loading-spinner'
export { ErrorBoundary, DefaultErrorFallback, InlineErrorFallback } from './common/error-boundary'
export { EmptyState, InlineEmptyState } from './common/empty-state'

// Home page components
export { HeroSection } from './home/<USER>'
export { StatsSection } from './home/<USER>'
export { FeaturedMallsSection } from './home/<USER>'
export { FeaturesSection } from './home/<USER>'

// Mall components
export { MallHeader } from './mall/mall-header'
export { ShopFilters } from './mall/shop-filters'
export { ShopsGrid } from './mall/shops-grid'

// Re-export existing components
export { default as AnimatedNumber } from './animated-number'
export { default as FeaturedMallCard } from './featured-mall-card'
export { default as ShopCard } from './shop-card'
export { Navigation } from './navigation'
export { Footer } from './footer'
