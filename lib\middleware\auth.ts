import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { ApiError } from '../api-error';
import { JwtPayload } from '../services/auth.service';

export const authMiddleware = async (req: NextRequest)=> {
  try {
    const cookie = req.cookies.get('token');
    
    if (!cookie?.value) {
      throw new ApiError(401, 'Unauthorized - No token provided');
    }
    
    const token = cookie.value;
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    return decoded;
  } catch (error) {
    throw new ApiError(401, 'Unauthorized - Invalid token');
  }
};