import { create } from 'zustand'
import { getAmenities } from '../api-functions'

interface AmenitiesState {
  amenities: string[]
  loading: boolean
  error: string | null

  // Actions
  fetchAmenities: () => Promise<void>
  clearError: () => void
}

const useAmenitiesStore = create<AmenitiesState>((set, get) => ({
  amenities: [],
  loading: false,
  error: null,

  fetchAmenities: async () => {
    // Don't fetch if already loaded
    if (get().amenities.length > 0) return

    try {
      set({ loading: true, error: null })

      const amenities = await getAmenities()

      set({
        amenities: amenities || [],
        loading: false
      })
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch amenities',
        loading: false
      })
    }
  },

  clearError: () => set({ error: null }),
}))

export default useAmenitiesStore
