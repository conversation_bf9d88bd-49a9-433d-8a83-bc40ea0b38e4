'use client'

import { useState, useEffect, useCallback } from 'react'
import { api } from '@/lib/api/client'
import { FeaturedMallData, MallDetailsPageData, ShopCardData, GetMallsOptions } from '@/lib/types/mall'
import { ApiResponse } from '@/lib/types/api-response'

// Hook for fetching malls list
export function useMalls(options: Partial<GetMallsOptions> = {}) {
  const [data, setData] = useState<FeaturedMallData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [meta, setMeta] = useState<any>(null)

  const fetchMalls = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const params = new URLSearchParams()
      if (options.page) params.append('page', options.page.toString())
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.searchTerm) params.append('searchTerm', options.searchTerm)
      if (options.category) params.append('category', options.category)
      if (options.city) params.append('city', options.city)
      if (options.state) params.append('state', options.state)
      if (options.country) params.append('country', options.country)
      if (options.activeOnly !== undefined) params.append('activeOnly', options.activeOnly.toString())
      if (options.sortBy) params.append('sortBy', options.sortBy)
      if (options.amenities?.length) {
        options.amenities.forEach(amenity => params.append('amenities', amenity))
      }

      const response = await api.get<FeaturedMallData[]>(`/malls?${params.toString()}`)
      
      if (response.success) {
        setData(response.data || [])
        setMeta(response.meta)
      } else {
        setError(response.error || 'Failed to fetch malls')
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching malls')
    } finally {
      setLoading(false)
    }
  }, [options])

  useEffect(() => {
    fetchMalls()
  }, [fetchMalls])

  return {
    data,
    loading,
    error,
    meta,
    refetch: fetchMalls,
  }
}

// Hook for fetching mall details by slug
export function useMallDetails(slug: string) {
  const [data, setData] = useState<MallDetailsPageData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchMallDetails = useCallback(async () => {
    if (!slug) return

    try {
      setLoading(true)
      setError(null)
      
      const response = await api.get<MallDetailsPageData>(`/malls/${slug}`)
      
      if (response.success) {
        setData(response.data || null)
      } else {
        setError(response.error || 'Failed to fetch mall details')
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching mall details')
    } finally {
      setLoading(false)
    }
  }, [slug])

  useEffect(() => {
    fetchMallDetails()
  }, [fetchMallDetails])

  return {
    data,
    loading,
    error,
    refetch: fetchMallDetails,
  }
}

// Hook for fetching mall shops
export function useMallShops(
  mallSlug: string,
  options: {
    page?: number
    limit?: number
    searchTerm?: string
    category?: string
    floor?: string
  } = {}
) {
  const [data, setData] = useState<ShopCardData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [meta, setMeta] = useState<any>(null)

  const fetchMallShops = useCallback(async () => {
    if (!mallSlug) return

    try {
      setLoading(true)
      setError(null)
      
      const params = new URLSearchParams()
      if (options.page) params.append('page', options.page.toString())
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.searchTerm) params.append('searchTerm', options.searchTerm)
      if (options.category && options.category !== 'All') params.append('category', options.category)
      if (options.floor && options.floor !== 'All') params.append('floor', options.floor)

      const response = await api.get<ShopCardData[]>(`/malls/${mallSlug}/shops?${params.toString()}`)
      
      if (response.success) {
        if (options.page === 1) {
          setData(response.data || [])
        } else {
          setData(prev => [...prev, ...(response.data || [])])
        }
        setMeta(response.meta)
      } else {
        setError(response.error || 'Failed to fetch mall shops')
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching mall shops')
    } finally {
      setLoading(false)
    }
  }, [mallSlug, options])

  useEffect(() => {
    fetchMallShops()
  }, [fetchMallShops])

  return {
    data,
    loading,
    error,
    meta,
    refetch: fetchMallShops,
  }
}
