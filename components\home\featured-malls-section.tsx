import Link from "next/link"
import { <PERSON>R<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FeaturedMallData } from "@/lib/types/mall"
import FeaturedMallCard from "@/components/featured-mall-card"

interface FeaturedMallsSectionProps {
  featuredMalls: FeaturedMallData[]
}

export function FeaturedMallsSection({ featuredMalls }: FeaturedMallsSectionProps) {
  return (
    <section className="py-24 relative">
      <div className="container px-4 md:px-6">
        <div className="text-center space-y-6 mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-secondary/10 dark:bg-secondary/5 border border-secondary/20">
            <span className="text-sm font-medium text-secondary">Featured</span>
          </div>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
            Popular Destinations
          </h2>
          <p className="mx-auto max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
            Discover our most popular shopping malls with unique experiences and diverse retail options.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredMalls.map((mall, index) => (
            <div 
              key={mall.id} 
              className="transform transition-all duration-500 hover:-translate-y-2"
              style={{
                animationDelay: `${index * 100}ms`,
                animation: 'fadeInUp 0.6s ease-out forwards'
              }}
            >
              <FeaturedMallCard mall={mall} />
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <Button asChild variant="outline" size="lg" className="text-lg px-8 py-4 rounded-xl border-2 hover:bg-primary/5 dark:hover:bg-primary/10 group transition-all duration-300">
            <Link href="/malls">
              View All Malls
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
