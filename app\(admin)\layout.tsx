"use client"
import { AdminSidebar } from "@/components/admin/sidebar"
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"
import { 
  Search, 
  Plus, 
  Moon,
  Sun,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { usePathname } from "next/navigation"
import { useAuthStore } from "@/hooks"
import { useTheme } from "next-themes"

interface AdminLayoutProps {
  children: React.ReactNode
}

// Dynamic breadcrumb generation based on pathname
function generateBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs = []
  
  if (segments.length === 0 || (segments.length === 1 && segments[0] === 'admin')) {
    return [{ title: 'Dashboard', href: '/admin', isPage: true }]
  }
  
  let currentPath = ''
  
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i]
    currentPath += `/${segment}`
    
    let title = segment.charAt(0).toUpperCase() + segment.slice(1)
    
    // Custom titles for specific routes
    const titleMap: Record<string, string> = {
      'admin': 'Admin',
      'malls': 'Malls',
      'shops': 'Shops',
      'users': 'Users',
      'analytics': 'Analytics',
      'settings': 'Settings',
      'new': 'Add New',
      'edit': 'Edit',
      'categories': 'Categories',
      'pending': 'Pending Approvals'
    }
    
    if (titleMap[segment]) {
      title = titleMap[segment]
    }
    
    breadcrumbs.push({
      title,
      href: currentPath,
      isPage: i === segments.length - 1
    })
  }
  
  return breadcrumbs
}

function AdminHeader({ pathname }: { pathname: string }) {
  const breadcrumbs = generateBreadcrumbs(pathname)
  const { theme, setTheme } = useTheme()
  
  return (
    <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center gap-2 px-4 flex-1">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        
        {/* Breadcrumbs */}
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.map((crumb, index) => (
              <div key={crumb.href} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                <BreadcrumbItem className="hidden md:block">
                  {crumb.isPage ? (
                    <BreadcrumbPage className="font-medium">{crumb.title}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={crumb.href} className="hover:text-foreground transition-colors">
                      {crumb.title}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      
      {/* Header Actions */}
      <div className="flex items-center gap-2 px-4">
        {/* Global Search */}
        <div className="relative hidden sm:block">
          <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input 
            placeholder="Search anything..." 
            className="w-64 pl-8 h-9 bg-background/50"
          />
        </div>
        
        {/* Quick Actions */}
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={() => setTheme(theme === "light" ? "dark" : "light")}
          className="relative rounded-2xl bg-muted/30 dark:bg-muted/20 hover:bg-primary/10 dark:hover:bg-primary/10 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg"
        >
          <Sun className="h-5 w-5 rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0 text-primary" />
          <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100 text-primary" />
          <span className="sr-only">Toggle theme</span>
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Quick Add</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem asChild>
              <a href="/admin/malls/new">Add New Mall</a>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <a href="/admin/shops/new">Add New Shop</a>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const { isAuthenticated } = useAuthStore();
  
  if (isAuthenticated == false) {
    return <div>Unauthorized</div>;
  }
  
  return (
    <SidebarProvider>
      <AdminSidebar />
      <SidebarInset className="bg-gradient-to-br from-background to-muted/20">
        <div className="flex flex-col h-full">
          <AdminHeader pathname={pathname} />
          <main className="flex-1 overflow-auto">
            <div className="flex flex-col gap-4 h-full p-3">
              {children}
            </div>
          </main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}