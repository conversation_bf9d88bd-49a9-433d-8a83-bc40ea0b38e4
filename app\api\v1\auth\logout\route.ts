import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { errorHandler } from '@/lib/api-error';

export async function GET(request: NextRequest) {
  try {
    (await cookies()).delete('token');
    (await cookies()).delete('refreshToken');
    return NextResponse.redirect(new URL('/', request.url));
  } catch (error) {
    return errorHandler(error);
  }
}
