"use client"
import React, { useState, useEffect } from 'react';
import { Navigation, ExternalLink, X, Layers, MapPin, Maximize2 } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, <PERSON>up, Marker } from 'react-leaflet';
import L from 'leaflet';

// Import Leaflet CSS
import 'leaflet/dist/leaflet.css';

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});


// Custom red marker icon
const customIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 28.5 12.5 28.5s12.5-16 12.5-28.5C25 5.6 19.4 0 12.5 0z" fill="#dc2626"/>
      <circle cx="12.5" cy="12.5" r="7" fill="white"/>
      <circle cx="12.5" cy="12.5" r="4" fill="#dc2626"/>
    </svg>
  `),
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
});

export default function MapModal({
  open,
  onClose,
  lat,
  lng,
  zoom,
  popup,
  mallName = '',
  address = ''
}: {
  open: boolean;
  onClose: () => void;
  lat: number;
  lng: number;
  zoom: number;
  popup: string;
  mallName?: string;
  address?: string;
}) {
  const [mapType, setMapType] = useState('street');
  const [isLoading, setIsLoading] = useState(true);

  // Handle body scroll lock
  useEffect(() => {
    if (open) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';

      // Simulate map loading
      const timer = setTimeout(() => setIsLoading(false), 500);

      return () => {
        document.body.style.overflow = originalStyle;
        clearTimeout(timer);
      };
    }
  }, [open]);

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;

  const streetTileUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
  const satelliteTileUrl = 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';

  const streetAttribution = '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';
  const satelliteAttribution = 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community';

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50">
      {/* Enhanced Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-md"
        onClick={handleBackdropClick}
      ></div>

      {/* Modal Container */}
      <div className="flex items-center justify-center min-h-screen p-4 sm:p-6">
        <div
          className="relative w-full max-w-6xl h-full bg-background/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-border/50 overflow-hidden transform animate-in zoom-in-95 duration-300"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Enhanced Header */}
          <div className="relative border-b border-border/50 bg-gradient-to-r from-background/95 via-background to-background/95 backdrop-blur-sm">
            <div className="flex items-center justify-between p-6">
              <div className="space-y-2 flex-1">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/5 dark:to-primary/10">
                    <MapPin className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold bg-gradient-to-br from-foreground to-foreground/80 bg-clip-text text-transparent">
                      {mallName || "Location Details"}
                    </h3>
                    <p className="text-sm text-primary font-medium">{popup}</p>
                  </div>
                </div>
                {address && (
                  <p className="text-muted-foreground ml-12">{address}</p>
                )}
              </div>

              <div className="flex items-center gap-3">
                {/* Map Type Toggle */}
                <div className="bg-muted/50 rounded-xl p-1 backdrop-blur-sm">
                  <button
                    onClick={() => setMapType(mapType === 'street' ? 'satellite' : 'street')}
                    className="px-4 py-2 text-sm font-medium rounded-lg hover:bg-background/80 transition-all duration-200 flex items-center gap-2 group"
                  >
                    <Layers className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
                    {mapType === 'street' ? 'Satellite' : 'Street'}
                  </button>
                </div>

                <button
                  onClick={onClose}
                  className="inline-flex items-center justify-center w-10 h-10 rounded-xl bg-muted/50 hover:bg-muted transition-all duration-200 transform hover:scale-105"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Map Content Area */}
          <div className="relative flex-1 h-[55vh]">
            {isLoading ? (
              // Enhanced Loading State
              <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-background to-muted/10 flex items-center justify-center">
                <div className="text-center space-y-6">
                  <div className="relative">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center animate-pulse">
                      <MapPin className="w-8 h-8 text-primary animate-bounce" />
                    </div>
                  </div>
                  <div className="space-y-3">
                    <h4 className="text-lg font-semibold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                      Loading Interactive Map
                    </h4>
                    <p className="text-muted-foreground max-w-md">
                      Preparing your location with satellite imagery and street details...
                    </p>
                    <div className="flex justify-center">
                      <div className="flex space-x-1">
                        {[0, 1, 2].map((i) => (
                          <div
                            key={i}
                            className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                            style={{ animationDelay: `${i * 0.2}s` }}
                          ></div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <MapContainer
                center={[lat, lng]}
                zoom={zoom}
                className="w-full h-full"
                zoomControl={true}
                scrollWheelZoom={true}
              >
                <TileLayer
                  attribution={mapType === 'street' ? streetAttribution : satelliteAttribution}
                  url={mapType === 'street' ? streetTileUrl : satelliteTileUrl}
                />
                <Marker position={[lat, lng]} icon={customIcon}>
                  <Popup>
                    <div className="text-center">
                      <strong>{mallName || popup}</strong>
                      {address && (
                        <>
                          <br />
                          <span className="text-sm text-gray-600">{address}</span>
                        </>
                      )}
                    </div>
                  </Popup>
                </Marker>
              </MapContainer>
            )}
          </div>

          {/* Enhanced Footer */}
          <div className="relative border-t border-border/50 bg-gradient-to-r from-background/95 via-background to-background/95 backdrop-blur-sm">
            <div className="p-6">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="hidden sm:flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Maximize2 className="w-3 h-3" />
                      <span>Click and drag to explore</span>
                    </div>
                    <span className="text-muted-foreground/50">•</span>
                    <span>Zoom with mouse wheel</span>
                  </div>
                </div>

                <div className="flex gap-3">
                  <a
                    href={googleMapsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 h-11 px-6 rounded-xl border-2 border-border/50 bg-background/80 hover:bg-primary/5 dark:hover:bg-primary/10 backdrop-blur-sm transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg text-sm font-medium group"
                  >
                    <ExternalLink className="w-4 h-4 transition-transform group-hover:scale-110" />
                    Google Maps
                  </a>

                  <a
                    href={directionsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 h-11 px-6 rounded-xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 text-sm font-medium group"
                  >
                    <Navigation className="w-4 h-4 transition-transform group-hover:translate-x-0.5" />
                    Get Directions
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}