"use client";
import {
  useMotionValue,
  animate,
  useInView,
} from "motion/react";
import { useEffect, useRef, useState } from "react";

interface AnimatedNumberProps {
  start?: number;
  end: number;
  duration?: number; // seconds
  className?: string;
  suffix?: string;
  locale?: string;
  triggerOnce?: boolean;
}

export default function AnimatedNumber({
  start = 0,
  end,
  duration = 2,
  className = "",
  suffix = "",
  locale = "en-US",
  triggerOnce = true,
}: AnimatedNumberProps) {
  const ref = useRef(null);
  const inView = useInView(ref, { amount: 0.5, once: triggerOnce });

  const motionValue = useMotionValue(start);
  const [displayValue, setDisplayValue] = useState(start);

  useEffect(() => {
    if (!inView) return;

    const controls = animate(motionValue, end, {
      duration,
      onUpdate(value) {
        setDisplayValue(Math.floor(value));
      },
    });

    return () => controls.stop();
  }, [inView, end, duration, motionValue]);

  return (
    <span ref={ref} className={className}>
      {new Intl.NumberFormat(locale).format(displayValue)}
      {suffix}
    </span>
  );
}
