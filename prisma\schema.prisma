generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

generator zod {
  provider = "zod-prisma-types"
  output   = "../lib/generated/zod"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(uuid())
  name        String
  email       String   @unique
  password    String
  phone       String?
  avatar      String?
  role        UserRole @default(user)
  isActive    Boolean  @default(true)
  lastLoginAt DateTime?
  preferences Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  pageViews   PageView[]
  reports     Report[]
  @@index([email])
  @@index([isActive])
  @@index([createdAt])
}

model PageView {
  id         String   @id @default(uuid())
  userId     String?
  user       User?    @relation(fields: [userId], references: [id])
  page       String
  title      String?
  referrer   String?
  userAgent  String?
  ipAddress  String?
  sessionId  String?
  duration   Int?
  viewedAt   DateTime @default(now())
  @@index([userId])
  @@index([page])
  @@index([viewedAt])
  @@index([sessionId])
}

model Report {
  id          String     @id @default(uuid())
  title       String
  description String?
  type        ReportType
  data        Json
  filters     Json?
  dateFrom    DateTime
  dateTo      DateTime
  generatedBy String
  user        User       @relation(fields: [generatedBy], references: [id])
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  @@index([type])
  @@index([generatedBy])
  @@index([createdAt])
}

model Mall {
  id               String      @id @default(uuid())
  name             String
  slug             String      @unique
  description      String?
  shortDescription String?
  address          String?
  city             String?
  state            String?
  country          String?
  pinCode          String?
  lat              Float?
  lng              Float?
  landmark         String?
  phone            String?
  email            String?
  customerCare     String?
  emergency        String?
  images           String[]
  logo             String?
  rating           Float?
  featured         Boolean     @default(false)
  mallHours        MallHour[]
  amenities        String[] // New field replacing Amenity model
  events           Event[]
  offers           Offer[]
  parkingAvailable Boolean      @default(false)
  parkingCapacity  Int?
  parkingType      ParkingType? @default(free)
  parkingRates     String?
  totalShops       Int?
  averageFootFall  Int?
  yearOpened       Int?
  company          String?
  manager          String?
  contactPerson    String?
  floors           Floor[]
  shops            Shop[]
  isActive         Boolean     @default(true)
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
  @@index([name])
  @@index([city])
  @@index([isActive])
  @@index([createdAt])
}

model MallHour {
  id       String  @id @default(uuid())
  mallId   String
  mall     Mall    @relation(fields: [mallId], references: [id])
  day      Int
  open     String
  close    String
  isClosed Boolean @default(false)
}

model Event {
  id                String   @id @default(uuid())
  mallId            String
  mall              Mall     @relation(fields: [mallId], references: [id])
  title             String
  description       String
  date              DateTime
  endDate           DateTime
  image             String?
  location          String?
  registerationLink String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  @@index([mallId])
}

model Offer {
  id          String   @id @default(uuid())
  mallId      String
  mall        Mall     @relation(fields: [mallId], references: [id])
  title       String
  description String
  validFrom   DateTime
  validTo     DateTime
  terms       String?
  shops       Shop[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  @@index([mallId])
}

model Floor {
  id         String   @id @default(uuid())
  mallId     String
  mall       Mall     @relation(fields: [mallId], references: [id])
  number     Int
  name       String
  mapImage   String?
  amenities  String[]
  categories String[]
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  @@index([mallId])
}

model Shop {
  id             String     @id @default(uuid())
  category       String
  mallId         String
  mall           Mall       @relation(fields: [mallId], references: [id])
  name           String
  description    String?
  phone          String?
  images         String[]
  email          String?
  website        String?
  hours          ShopHour[]
  floor          Int
  wing           String?
  coordinates    String?
  socialMedia    Json?
  image          String?
  logo           String?
  offerId        String?
  Offer          Offer?     @relation(fields: [offerId], references: [id])
  featured       Boolean    @default(false)
  specialFeatures String[]
  rating         Float?
  tags           String[]
  openingDate    DateTime?
  isActive       Boolean    @default(true)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  @@index([name])
  @@index([floor])
  @@index([wing])
  @@index([offerId])
  @@index([mallId])
  @@index([createdAt])
}

model ShopHour {
  id       String  @id @default(uuid())
  shopId   String
  shop     Shop    @relation(fields: [shopId], references: [id])
  day      Int
  open     String
  close    String
  isClosed Boolean @default(false)
}

enum ParkingType {
  free
  paid
  valet
}

enum UserRole {
  admin
  user
  manager
}

enum ReportType {
  MALL_ANALYTICS
  USER_ANALYTICS
  SHOP_ANALYTICS
  PAGE_VIEW_ANALYTICS
  CUSTOM
}