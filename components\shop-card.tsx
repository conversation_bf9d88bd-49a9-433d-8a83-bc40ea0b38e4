import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import Link from 'next/link'
import { Badge } from './ui/badge'
import { Clock, Phone, Store } from 'lucide-react'
import { motion } from 'motion/react'
import { ShopCardData } from '@/lib/types/mall'
import Image from 'next/image'

export default function ShopCard({ shop, url }: { shop: ShopCardData; url: string }) {
    return <motion.div
        initial={{ opacity: 0, y: 50 }}
        transition={{ duration: 0.3 }}
        whileInView={{ opacity: 1, y: 0 }}
        className="group"
    >
        <Card className="overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-background to-muted/20 border-0 backdrop-blur-sm">
            <div className="relative overflow-hidden">
                <Image
                    src={shop.image || "/placeholder.svg"}
                    alt={shop.name}
                    width={300}
                    height={200}
                    className="w-full h-40 object-cover transition-all duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-3 right-3">
                    <Badge className="bg-primary/90 text-primary-foreground backdrop-blur-md shadow-lg">
                        Floor {shop.floor}
                    </Badge>
                </div>
                <div className="absolute bottom-3 left-3 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg backdrop-blur-md">
                        {shop.category}
                    </Badge>
                </div>
            </div>
            <CardHeader className="pb-3">
                <CardTitle className="group-hover:text-primary transition-colors line-clamp-1 text-lg">
                    {shop.name}
                </CardTitle>
                <CardDescription className="line-clamp-2">{shop.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 pt-0">
                <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 text-primary" />
                        <span>{shop.hours?.[0]?.open} - {shop.hours?.[0]?.close}</span>
                    </div>
                    {shop.phone && (
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Phone className="h-3 w-3 text-primary" />
                            <span>{shop.phone}</span>
                        </div>
                    )}
                </div>
                <Button
                    asChild
                    className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md hover:shadow-lg transition-all duration-300 group/btn"
                    size="sm"
                >
                    <Link href={url}>
                        <span>View Details</span>
                        <Store className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                    </Link>
                </Button>
            </CardContent>
        </Card>
    </motion.div>
}

export const ShopCardSkeleton = () => (
    <Card className="overflow-hidden">
        <div className="w-full h-40 bg-muted animate-pulse" />
        <CardHeader>
            <div className="h-5 bg-muted rounded animate-pulse mb-2" />
            <div className="h-4 bg-muted rounded w-2/3 animate-pulse" />
        </CardHeader>
        <CardContent className="space-y-3">
            <div className="h-3 bg-muted rounded animate-pulse" />
            <div className="h-3 bg-muted rounded w-3/4 animate-pulse" />
            <div className="h-8 bg-muted rounded animate-pulse" />
        </CardContent>
    </Card>
)